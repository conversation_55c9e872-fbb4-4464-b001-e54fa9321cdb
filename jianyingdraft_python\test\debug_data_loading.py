"""
调试数据加载问题
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.storage.data_store import DataStore
from pathlib import Path


def debug_data_loading():
    """调试数据加载"""
    print("🔍 调试数据加载...")
    
    data_dir = Path("jianyingdraft_python/data")
    print(f"📂 数据目录: {data_dir.absolute()}")
    
    # 检查目录结构
    print("\n=== 目录结构 ===")
    if data_dir.exists():
        for item in data_dir.iterdir():
            print(f"📁 {item.name}")
            if item.is_dir():
                for sub_item in item.iterdir():
                    print(f"   📄 {sub_item.name}")
    else:
        print("❌ 数据目录不存在")
        return
    
    # 手动测试加载过程
    print("\n=== 手动测试加载过程 ===")
    
    # 创建数据存储实例
    try:
        data_store = DataStore(str(data_dir))
        print("✅ DataStore 实例创建成功")
    except Exception as e:
        print(f"❌ DataStore 实例创建失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 检查内存中的数据
    print(f"\n📊 内存中的数据:")
    print(f"  草稿数量: {len(data_store._drafts)}")
    print(f"  视频片段数量: {len(data_store._video_segments)}")
    print(f"  音频片段数量: {len(data_store._audio_segments)}")
    print(f"  文本片段数量: {len(data_store._text_segments)}")
    print(f"  轨道数量: {len(data_store._tracks)}")
    
    # 详细检查草稿数据
    if data_store._drafts:
        print(f"\n📋 草稿详情:")
        for draft_id, draft in data_store._drafts.items():
            print(f"  ID: {draft_id}")
            print(f"  名称: {draft.draft_name}")
            print(f"  路径: {draft.draft_path}")
    else:
        print("\n❌ 没有加载到草稿数据")
        
        # 手动尝试加载草稿文件夹
        print("\n🔧 手动尝试加载草稿文件夹...")
        for folder_path in data_dir.iterdir():
            if folder_path.is_dir() and folder_path.name != "backup_old_format":
                print(f"\n📁 检查文件夹: {folder_path.name}")
                
                draft_file = folder_path / "draft.json"
                if draft_file.exists():
                    print(f"   ✅ 找到 draft.json")
                    try:
                        import json
                        with open(draft_file, 'r', encoding='utf-8') as f:
                            draft_data = json.load(f)
                        print(f"   📄 文件内容: {draft_data}")
                        
                        # 尝试手动加载
                        data_store._load_draft_folder_data(folder_path)
                        print(f"   ✅ 手动加载成功")
                        
                    except Exception as e:
                        print(f"   ❌ 手动加载失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"   ❌ 未找到 draft.json")
    
    # 再次检查内存中的数据
    print(f"\n📊 手动加载后的数据:")
    print(f"  草稿数量: {len(data_store._drafts)}")
    
    if data_store._drafts:
        print(f"\n📋 草稿详情:")
        for draft_id, draft in data_store._drafts.items():
            print(f"  ID: {draft_id}")
            print(f"  名称: {draft.draft_name}")
            print(f"  类型: {type(draft)}")


if __name__ == "__main__":
    debug_data_loading()
