"""
特效控制器
提供特效相关的接口
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/effects", tags=["特效接口"])

# 使用集中式存储管理器
print("🔧 EffectController: 初始化服务...")
print("✅ EffectController: 服务初始化完成")


@router.get("/all_types", response_model=DataResponse[Dict[str, Any]])
async def get_all_types():
    """获取所有特效类型"""
    try:
        # 返回特效类型映射
        effect_types = {
            "video_effects": {
                "scene_effects": "场景特效",
                "character_effects": "人物特效",
                "transition_effects": "转场特效"
            },
            "audio_effects": {
                "scene_effects": "场景音效",
                "tone_effects": "音色特效",
                "speech_to_song": "声音成曲"
            },
            "filters": {
                "basic_filters": "基础滤镜",
                "beauty_filters": "美颜滤镜",
                "style_filters": "风格滤镜"
            },
            "masks": {
                "shape_masks": "形状蒙版",
                "custom_masks": "自定义蒙版"
            }
        }
        
        return DataResponse.success(effect_types)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("", response_model=DataResponse[List[Dict[str, Any]]])
async def get_effects(
    effect_type: str = Query(..., description="特效类型"),
    is_vip: Optional[bool] = Query(None, description="是否为会员")
):
    """获取所有特效"""
    try:
        # 根据特效类型返回相应的特效列表
        effects = []
        
        if effect_type == "video_scene_effects":
            effects = [
                {
                    "id": "video_effect_001",
                    "name": "模糊效果",
                    "resource_id": "blur_effect",
                    "effect_id": "blur_001",
                    "category": "场景特效",
                    "is_vip": False,
                    "params": [
                        {"name": "强度", "min": 0, "max": 100, "default": 50},
                        {"name": "半径", "min": 0, "max": 50, "default": 10}
                    ]
                },
                {
                    "id": "video_effect_002", 
                    "name": "发光效果",
                    "resource_id": "glow_effect",
                    "effect_id": "glow_001",
                    "category": "场景特效",
                    "is_vip": True,
                    "params": [
                        {"name": "强度", "min": 0, "max": 100, "default": 70},
                        {"name": "颜色", "min": 0, "max": 360, "default": 180}
                    ]
                }
            ]
        elif effect_type == "audio_scene_effects":
            effects = [
                {
                    "id": "audio_effect_001",
                    "name": "回声效果",
                    "resource_id": "echo_effect",
                    "effect_id": "echo_001",
                    "category": "场景音效",
                    "is_vip": False,
                    "params": [
                        {"name": "延迟", "min": 0, "max": 100, "default": 30},
                        {"name": "反馈", "min": 0, "max": 100, "default": 40}
                    ]
                }
            ]
        elif effect_type == "filters":
            effects = [
                {
                    "id": "filter_001",
                    "name": "复古滤镜",
                    "resource_id": "vintage_filter",
                    "effect_id": "vintage_001",
                    "category": "风格滤镜",
                    "is_vip": False,
                    "params": [
                        {"name": "强度", "min": 0, "max": 100, "default": 80}
                    ]
                }
            ]
        elif effect_type == "masks":
            effects = [
                {
                    "id": "mask_001",
                    "name": "圆形蒙版",
                    "resource_id": "circle_mask",
                    "effect_id": "circle_001",
                    "category": "形状蒙版",
                    "is_vip": False,
                    "params": [
                        {"name": "大小", "min": 0, "max": 100, "default": 50},
                        {"name": "羽化", "min": 0, "max": 100, "default": 0}
                    ]
                }
            ]
        
        # 如果指定了VIP状态，过滤结果
        if is_vip is not None:
            effects = [effect for effect in effects if effect["is_vip"] == is_vip]
        
        return DataResponse.success(effects)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
