"""
测试所有新增的API接口
验证特效、滤镜、蒙版、转场等功能
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.effect_controller import router as effect_router
from jianyingdraft_python.controller.video_effect_controller import video_effect_service
from jianyingdraft_python.controller.audio_effect_controller import audio_effect_service
from jianyingdraft_python.controller.transition_controller import transition_service
from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.controller.track_controller import track_service
from jianyingdraft_python.controller.video_segment_controller import video_service

from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.audio_effect_req_dto import AudioEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto
from jianyingdraft_python.domain.req.transition_req_dto import TransitionReqDto

from jianyingdraft_python.domain.video.video_effect import VideoEffect
from jianyingdraft_python.domain.video.video_filter import VideoFilter
from jianyingdraft_python.domain.video.video_mask import VideoMask
from jianyingdraft_python.domain.audio.audio_effect import AudioEffect
from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.domain.req.transition_type import TransitionType
from jianyingdraft_python.domain.timerange import Timerange


def test_all_new_apis():
    """测试所有新增的API接口"""
    print("🚀 开始测试所有新增的API接口...")
    
    # 1. 创建测试草稿
    print("\n=== 1. 创建测试草稿 ===")
    draft_req = DraftCreateReqDto(
        name="新API测试草稿",
        width=1920,
        height=1080,
        fps=30,
        draftPath="/test/new_api_draft"
    )
    
    try:
        draft = draft_service.create_draft_script(draft_req)
        draft_id = draft.draftId  # 使用正确的字段名
        print(f"✅ 草稿创建成功: {draft_id}")
    except Exception as e:
        print(f"❌ 草稿创建失败: {e}")
        return False
    
    # 2. 创建测试轨道
    print("\n=== 2. 创建测试轨道 ===")
    track_req = TrackAddReqDto(
        draftId=draft_id,
        trackType="video",
        trackName="新API测试轨道",
        mute=False,
        relativeIndex=0,
        absoluteIndex=1
    )
    
    try:
        track = track_service.add_track(track_req)
        track_id = track.id
        print(f"✅ 轨道创建成功: {track_id}")
    except Exception as e:
        print(f"❌ 轨道创建失败: {e}")
        return False
    
    # 3. 创建测试视频片段
    print("\n=== 3. 创建测试视频片段 ===")
    video_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/new_api_video.mp4",
        trackId=track_id,
        speed=1.0,
        volume=1.0
    )
    
    try:
        video_segment = video_service.add_video_segment(video_req)
        video_segment_id = video_segment.id
        print(f"✅ 视频片段创建成功: {video_segment_id}")
    except Exception as e:
        print(f"❌ 视频片段创建失败: {e}")
        return False
    
    # 4. 测试视频特效API
    print("\n=== 4. 测试视频特效API ===")
    try:
        # 创建视频特效
        video_effect = VideoEffect(
            effect_type=Resource(
                resource_id="blur_effect",
                name="模糊效果"
            ),
            params=[50.0, 10.0]  # 强度50，半径10
        )
        
        effect_req = VideoEffectReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            effects=[video_effect]
        )
        
        result = video_effect_service.add_video_effects(effect_req)
        print(f"✅ 视频特效添加成功")
        print(f"   特效数量: {len(result.video_effects) if result.video_effects else 0}")
        
    except Exception as e:
        print(f"❌ 视频特效测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 测试视频滤镜API
    print("\n=== 5. 测试视频滤镜API ===")
    try:
        # 创建视频滤镜
        video_filter = VideoFilter(
            filter_type=Resource(
                resource_id="vintage_filter",
                name="复古滤镜"
            ),
            intensity=80.0
        )
        
        filter_req = VideoFilterReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            filters=[video_filter]
        )
        
        result = video_effect_service.add_video_filters(filter_req)
        print(f"✅ 视频滤镜添加成功")
        print(f"   滤镜数量: {len(result.video_filters) if result.video_filters else 0}")
        
    except Exception as e:
        print(f"❌ 视频滤镜测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. 测试视频蒙版API
    print("\n=== 6. 测试视频蒙版API ===")
    try:
        # 创建视频蒙版
        video_mask = VideoMask(
            mask_type=Resource(
                resource_id="circle_mask",
                name="圆形蒙版"
            ),
            center_x=0.0,
            center_y=0.0,
            size=0.5,
            rotation=0.0,
            feather=0.1,
            invert=False
        )
        
        mask_req = VideoMaskReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            mask=video_mask
        )
        
        result = video_effect_service.add_video_mask(mask_req)
        print(f"✅ 视频蒙版添加成功")
        print(f"   蒙版类型: {result.video_mask.mask_type.resource_id if result.video_mask else '无'}")
        
    except Exception as e:
        print(f"❌ 视频蒙版测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 7. 测试转场效果API
    print("\n=== 7. 测试转场效果API ===")
    try:
        # 创建第二个视频片段用于转场
        video_req2 = MediaSegmentAddReqDto(
            draftId=draft_id,
            targetTimerange=Timerange(start="5s", duration="5s"),
            resourcePath="/test/new_api_video2.mp4",
            trackId=track_id,
            speed=1.0,
            volume=1.0
        )
        
        video_segment2 = video_service.add_video_segment(video_req2)
        video_segment2_id = video_segment2.id
        print(f"✅ 第二个视频片段创建成功: {video_segment2_id}")
        
        # 创建转场效果
        transition_type = TransitionType(
            transition_type=Resource(
                resource_id="fade_transition",
                name="淡入淡出转场"
            ),
            duration="1s"
        )
        
        transition_req = TransitionReqDto(
            draft_id=draft_id,
            from_segment_id=video_segment_id,
            to_segment_id=video_segment2_id,
            transition_type=transition_type,
            duration="1s",
            track_id=track_id
        )
        
        result = transition_service.add_transition(transition_req)
        print(f"✅ 转场效果添加成功: {result.id}")
        print(f"   转场类型: {result.transition_type.transition_type.name}")
        
    except Exception as e:
        print(f"❌ 转场效果测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 8. 检查文件创建情况
    print("\n=== 8. 检查文件创建情况 ===")
    try:
        from jianyingdraft_python.config.app_config import get_app_config
        config = get_app_config()
        
        draft_folder = config.get_draft_folder_path("新API测试草稿", draft_id)
        print(f"📁 草稿文件夹: {draft_folder}")
        
        if draft_folder.exists():
            print(f"📄 文件夹内容:")
            for item in draft_folder.iterdir():
                size = item.stat().st_size if item.is_file() else 0
                print(f"   📄 {item.name} ({size} bytes)")
        else:
            print(f"❌ 草稿文件夹不存在")
            
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
    
    print(f"\n🎉 所有新增API接口测试完成！")
    return True


if __name__ == "__main__":
    success = test_all_new_apis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 新增API接口测试成功！")
        print("✅ 特效、滤镜、蒙版、转场功能已实现")
    else:
        print("❌ 新增API接口测试失败！")
        print("🔧 请检查相关实现")
