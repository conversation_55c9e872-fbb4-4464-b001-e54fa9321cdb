"""
素材工具服务
处理素材工具相关的业务逻辑
"""
import os
from typing import Optional
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.exception.sys_exception import SysException


class MaterialsUtilsService:
    """素材工具服务类"""
    
    def __init__(self):
        self.data_store: Optional[DataStore] = None
    
    def media_info(self, file_path: str) -> MediaInfo:
        """获取素材信息"""
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise SysException.not_found(f"文件不存在: {file_path}")
        
        # 获取文件基本信息
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        # 根据文件扩展名推断类型和MIME类型
        file_ext = os.path.splitext(file_name)[1].lower()
        
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
            mime_type = f"video/{file_ext[1:]}"
            media_type = file_ext[1:]
            # 视频文件默认10秒
            duration_microseconds = 10000000
            duration_seconds = "10s"
        elif file_ext in ['.mp3', '.wav', '.aac', '.flac', '.ogg']:
            mime_type = f"audio/{file_ext[1:]}"
            media_type = file_ext[1:]
            # 音频文件默认10秒
            duration_microseconds = 10000000
            duration_seconds = "10s"
        elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            mime_type = f"image/{file_ext[1:]}"
            media_type = file_ext[1:]
            # 图片文件默认5秒
            duration_microseconds = 5000000
            duration_seconds = "5s"
        else:
            mime_type = "application/octet-stream"
            media_type = "unknown"
            duration_microseconds = 0
            duration_seconds = "0s"
        
        # 创建MediaInfo对象
        media_info = MediaInfo(
            file_name=file_name,
            absolute_path=file_path,
            file_size=file_size,
            mime_type=mime_type,
            type=media_type,
            duration_microseconds=duration_microseconds,
            duration_seconds=duration_seconds
        )
        
        return media_info
