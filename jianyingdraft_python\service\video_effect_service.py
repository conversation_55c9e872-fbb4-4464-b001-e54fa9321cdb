"""
视频特效服务
处理视频特效、滤镜、蒙版相关的业务逻辑
"""
from typing import List, Optional
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException


class VideoEffectService:
    """视频特效服务类"""
    
    def __init__(self):
        self.data_store: Optional[DataStore] = None
    
    def add_video_effects(self, req_dto: VideoEffectReqDto) -> VideoSegmentEntity:
        """为视频片段添加特效"""
        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)
        
        # 添加特效（合并新特效到现有特效列表）
        if segment.video_effects is None:
            segment.video_effects = []
        
        # 根据resourceId去重，同一类型特效只能有一个
        existing_effect_types = {effect.effect_type.resource_id for effect in segment.video_effects}
        
        for new_effect in req_dto.effects:
            if new_effect.effect_type.resource_id not in existing_effect_types:
                segment.video_effects.append(new_effect)
            else:
                # 替换现有的同类型特效
                segment.video_effects = [
                    effect for effect in segment.video_effects 
                    if effect.effect_type.resource_id != new_effect.effect_type.resource_id
                ]
                segment.video_effects.append(new_effect)
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def add_video_filters(self, req_dto: VideoFilterReqDto) -> VideoSegmentEntity:
        """为视频片段添加滤镜"""
        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)
        
        # 添加滤镜（合并新滤镜到现有滤镜列表）
        if segment.video_filters is None:
            segment.video_filters = []
        
        # 根据resourceId去重，同一类型滤镜只能有一个
        existing_filter_types = {filter.filter_type.resource_id for filter in segment.video_filters}
        
        for new_filter in req_dto.filters:
            if new_filter.filter_type.resource_id not in existing_filter_types:
                segment.video_filters.append(new_filter)
            else:
                # 替换现有的同类型滤镜
                segment.video_filters = [
                    filter for filter in segment.video_filters 
                    if filter.filter_type.resource_id != new_filter.filter_type.resource_id
                ]
                segment.video_filters.append(new_filter)
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def add_video_mask(self, req_dto: VideoMaskReqDto) -> VideoSegmentEntity:
        """为视频片段添加蒙版"""
        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)
        
        # 设置蒙版（一个片段只能有一个蒙版，新蒙版会覆盖旧蒙版）
        segment.video_mask = req_dto.mask
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def remove_video_effects(self, draft_id: str, segment_id: str) -> VideoSegmentEntity:
        """移除视频片段的所有特效"""
        # 获取视频片段
        segment = self._get_video_segment(draft_id, segment_id)
        
        # 清空特效列表
        segment.video_effects = []
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def remove_video_filters(self, draft_id: str, segment_id: str) -> VideoSegmentEntity:
        """移除视频片段的所有滤镜"""
        # 获取视频片段
        segment = self._get_video_segment(draft_id, segment_id)
        
        # 清空滤镜列表
        segment.video_filters = []
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def remove_video_mask(self, draft_id: str, segment_id: str) -> VideoSegmentEntity:
        """移除视频片段的蒙版"""
        # 获取视频片段
        segment = self._get_video_segment(draft_id, segment_id)
        
        # 清空蒙版
        segment.video_mask = None
        
        # 保存更新后的片段
        self.data_store.save_video_segment(segment)
        
        return segment
    
    def get_video_effects(self, draft_id: str, segment_id: str) -> List:
        """获取视频片段的所有特效"""
        # 获取视频片段
        segment = self._get_video_segment(draft_id, segment_id)
        
        # 返回特效信息
        effects_info = {
            "video_effects": segment.video_effects or [],
            "video_filters": segment.video_filters or [],
            "video_mask": segment.video_mask
        }
        
        return [effects_info]
    
    def _get_video_segment(self, draft_id: str, segment_id: str) -> VideoSegmentEntity:
        """获取视频片段"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")
        
        # 获取视频片段
        segment = None
        for seg_id, seg in self.data_store._video_segments.items():
            if seg.id == segment_id and seg.draft_id == draft_id:
                segment = seg
                break
        
        if not segment:
            raise SysException.not_found("视频片段不存在")
        
        return segment
