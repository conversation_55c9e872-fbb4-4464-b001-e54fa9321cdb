"""
迁移存储格式：从旧的单文件格式迁移到新的文件夹格式
"""
import sys
import os
import json
import shutil
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.entity.draft_entity import DraftEntity


def clean_folder_name(name: str) -> str:
    """清理文件夹名称，移除不合法字符"""
    if not name or not name.strip():
        return ""
    return re.sub(r'[<>:"/\\|?*]', '_', name.strip())


def migrate_storage_format():
    """迁移存储格式"""
    print("🔄 开始迁移存储格式...")
    
    data_dir = Path("jianyingdraft_python/data")
    
    # 检查旧格式文件
    old_drafts_file = data_dir / "drafts.json"
    old_video_segments_file = data_dir / "video_segments.json"
    old_audio_segments_file = data_dir / "audio_segments.json"
    old_text_segments_file = data_dir / "text_segments.json"
    old_tracks_file = data_dir / "tracks.json"
    
    if not old_drafts_file.exists():
        print("❌ 未找到旧的drafts.json文件，无需迁移")
        return
    
    print(f"📂 数据目录: {data_dir.absolute()}")
    
    # 备份旧文件
    backup_dir = data_dir / "backup_old_format"
    backup_dir.mkdir(exist_ok=True)
    
    print("\n=== 1. 备份旧文件 ===")
    old_files = [old_drafts_file, old_video_segments_file, old_audio_segments_file, old_text_segments_file, old_tracks_file]
    for old_file in old_files:
        if old_file.exists():
            backup_file = backup_dir / old_file.name
            shutil.copy2(old_file, backup_file)
            print(f"✅ 备份: {old_file.name} -> {backup_file}")
    
    # 加载旧数据
    print("\n=== 2. 加载旧数据 ===")
    
    # 加载草稿数据
    with open(old_drafts_file, 'r', encoding='utf-8') as f:
        drafts_data = json.load(f)
    
    drafts = {}
    for draft_id, draft_dict in drafts_data.items():
        try:
            draft = DraftEntity(**draft_dict)
            drafts[draft_id] = draft
            print(f"✅ 加载草稿: {draft.draft_name or draft.id}")
        except Exception as e:
            print(f"❌ 加载草稿失败 {draft_id}: {e}")
    
    # 加载其他数据
    def load_json_file(file_path):
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"❌ 加载文件失败 {file_path}: {e}")
        return {}
    
    video_segments = load_json_file(old_video_segments_file)
    audio_segments = load_json_file(old_audio_segments_file)
    text_segments = load_json_file(old_text_segments_file)
    tracks = load_json_file(old_tracks_file)
    
    print(f"📊 数据统计:")
    print(f"  草稿: {len(drafts)}")
    print(f"  视频片段: {len(video_segments)}")
    print(f"  音频片段: {len(audio_segments)}")
    print(f"  文本片段: {len(text_segments)}")
    print(f"  轨道: {len(tracks)}")
    
    # 创建新的文件夹结构
    print("\n=== 3. 创建新的文件夹结构 ===")
    
    for draft_id, draft in drafts.items():
        # 确定文件夹名称
        folder_name = clean_folder_name(draft.draft_name) if draft.draft_name else draft_id
        draft_folder = data_dir / folder_name
        
        print(f"\n📁 处理草稿: {draft.draft_name or draft_id}")
        print(f"   文件夹: {folder_name}")
        
        # 创建草稿文件夹
        draft_folder.mkdir(exist_ok=True)
        
        # 保存草稿信息
        draft_file = draft_folder / "draft.json"
        draft_data = draft.model_dump()
        # 处理 datetime 字段
        for field_name, field_value in draft_data.items():
            if hasattr(field_value, 'isoformat'):
                draft_data[field_name] = field_value.isoformat()
        
        with open(draft_file, 'w', encoding='utf-8') as f:
            json.dump(draft_data, f, ensure_ascii=False, indent=2)
        print(f"   ✅ 保存: draft.json")
        
        # 保存该草稿的视频片段
        draft_video_segments = {k: v for k, v in video_segments.items() 
                               if v.get('draft_id') == draft_id}
        if draft_video_segments:
            video_file = draft_folder / "video_segments.json"
            with open(video_file, 'w', encoding='utf-8') as f:
                json.dump(draft_video_segments, f, ensure_ascii=False, indent=2)
            print(f"   ✅ 保存: video_segments.json ({len(draft_video_segments)} 个)")
        
        # 保存该草稿的音频片段
        draft_audio_segments = {k: v for k, v in audio_segments.items() 
                               if v.get('draft_id') == draft_id}
        if draft_audio_segments:
            audio_file = draft_folder / "audio_segments.json"
            with open(audio_file, 'w', encoding='utf-8') as f:
                json.dump(draft_audio_segments, f, ensure_ascii=False, indent=2)
            print(f"   ✅ 保存: audio_segments.json ({len(draft_audio_segments)} 个)")
        
        # 保存该草稿的文本片段
        draft_text_segments = {k: v for k, v in text_segments.items() 
                              if v.get('draft_id') == draft_id}
        if draft_text_segments:
            text_file = draft_folder / "text_segments.json"
            with open(text_file, 'w', encoding='utf-8') as f:
                json.dump(draft_text_segments, f, ensure_ascii=False, indent=2)
            print(f"   ✅ 保存: text_segments.json ({len(draft_text_segments)} 个)")
        
        # 保存该草稿的轨道
        draft_tracks = {k: v for k, v in tracks.items() 
                       if v.get('draft_id') == draft_id}
        if draft_tracks:
            tracks_file = draft_folder / "tracks.json"
            with open(tracks_file, 'w', encoding='utf-8') as f:
                json.dump(draft_tracks, f, ensure_ascii=False, indent=2)
            print(f"   ✅ 保存: tracks.json ({len(draft_tracks)} 个)")
    
    # 删除旧文件
    print("\n=== 4. 删除旧文件 ===")
    for old_file in old_files:
        if old_file.exists():
            old_file.unlink()
            print(f"🗑️ 删除: {old_file.name}")
    
    # 显示新的目录结构
    print("\n=== 5. 新的目录结构 ===")
    show_directory_structure(data_dir)
    
    print("\n✅ 迁移完成！")
    print(f"📁 备份文件位置: {backup_dir.absolute()}")
    print("💡 如果新格式工作正常，可以删除backup_old_format文件夹")


def show_directory_structure(root_dir, prefix="", max_depth=3, current_depth=0):
    """显示目录结构"""
    if current_depth >= max_depth:
        return
        
    try:
        items = sorted(os.listdir(root_dir))
        for i, item in enumerate(items):
            item_path = os.path.join(root_dir, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(item_path):
                next_prefix = prefix + ("    " if is_last else "│   ")
                show_directory_structure(item_path, next_prefix, max_depth, current_depth + 1)
    except PermissionError:
        print(f"{prefix}[Permission Denied]")


if __name__ == "__main__":
    migrate_storage_format()
