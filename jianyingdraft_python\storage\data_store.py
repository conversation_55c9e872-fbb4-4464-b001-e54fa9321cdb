import os
import json
import uuid
import re
from typing import Dict, Any, Optional, List
from pathlib import Path
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.entity.track_entity import TrackEntity
from jianyingdraft_python.entity.transition_entity import TransitionEntity


class DataStore:
    """
    数据存储管理器 - 每个草稿使用独立文件夹存储
    """

    def __init__(self, data_dir: str = "./data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)

        # 内存缓存
        self._drafts: Dict[str, DraftEntity] = {}
        self._video_segments: Dict[str, VideoSegmentEntity] = {}
        self._audio_segments: Dict[str, AudioSegmentEntity] = {}
        self._text_segments: Dict[str, TextSegmentEntity] = {}
        self._tracks: Dict[str, TrackEntity] = {}
        self._transitions: Dict[str, TransitionEntity] = {}

        # 加载数据
        self._load_all_data()
    
    def _get_draft_folder_name(self, draft_name: Optional[str], draft_id: str) -> str:
        """获取草稿文件夹名称"""
        if draft_name and draft_name.strip():
            # 清理文件夹名称，移除不合法字符
            folder_name = re.sub(r'[<>:"/\\|?*]', '_', draft_name.strip())
            return folder_name
        else:
            return draft_id

    def _get_draft_folder_path(self, draft_name: Optional[str], draft_id: str) -> Path:
        """获取草稿文件夹路径"""
        folder_name = self._get_draft_folder_name(draft_name, draft_id)
        return self.data_dir / folder_name

    def _load_all_data(self):
        """加载所有数据到内存"""
        # 遍历data目录下的所有文件夹
        if not self.data_dir.exists():
            return

        for folder_path in self.data_dir.iterdir():
            if folder_path.is_dir():
                self._load_draft_folder_data(folder_path)
    
    def _load_draft_folder_data(self, folder_path: Path):
        """从草稿文件夹加载数据"""
        try:
            # 加载草稿信息
            draft_file = folder_path / "draft.json"
            if draft_file.exists():
                draft_data = self._load_single_file(draft_file)
                if draft_data:
                    draft = DraftEntity(**draft_data)
                    self._drafts[draft.id] = draft

            # 加载各种片段数据
            self._load_segments_from_folder(folder_path, "video_segments.json", VideoSegmentEntity, self._video_segments)
            self._load_segments_from_folder(folder_path, "audio_segments.json", AudioSegmentEntity, self._audio_segments)
            self._load_segments_from_folder(folder_path, "text_segments.json", TextSegmentEntity, self._text_segments)
            self._load_segments_from_folder(folder_path, "tracks.json", TrackEntity, self._tracks)
            self._load_segments_from_folder(folder_path, "transitions.json", TransitionEntity, self._transitions)

        except Exception as e:
            print(f"Error loading folder {folder_path}: {e}")

    def _load_segments_from_folder(self, folder_path: Path, filename: str, entity_class, target_dict: Dict):
        """从文件夹中加载片段数据"""
        file_path = folder_path / filename
        if file_path.exists():
            data = self._load_data_from_file(file_path, entity_class)
            target_dict.update(data)

    def _load_single_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """从JSON文件加载单个对象数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 处理 datetime 字段
                self._parse_datetime_fields(data)
                return data
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None

    def _parse_datetime_fields(self, data: Dict[str, Any]):
        """解析datetime字段"""
        from datetime import datetime

        datetime_fields = ['create_time', 'update_time']
        for field in datetime_fields:
            if field in data and isinstance(data[field], str):
                try:
                    data[field] = datetime.fromisoformat(data[field])
                except Exception as e:
                    print(f"Error parsing datetime field {field}: {e}")

    def _load_data_from_file(self, file_path: Path, entity_class) -> Dict[str, Any]:
        """从JSON文件加载多个对象数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            result = {}
            for k, v in data.items():
                # 处理 datetime 字段
                self._parse_datetime_fields(v)
                result[k] = entity_class(**v)
            return result
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return {}
    
    def _save_single_data(self, file_path: Path, entity: Any):
        """保存单个实体到JSON文件"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用 model_dump 并处理 datetime 序列化
            entity_data = entity.model_dump()
            # 处理 datetime 字段
            for field_name, field_value in entity_data.items():
                if hasattr(field_value, 'isoformat'):  # datetime 对象
                    entity_data[field_name] = field_value.isoformat()

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(entity_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving {file_path}: {e}")

    def _save_multiple_data(self, file_path: Path, data: Dict[str, Any]):
        """保存多个实体到JSON文件"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 使用 model_dump 并处理 datetime 序列化
            serializable_data = {}
            for k, v in data.items():
                entity_data = v.model_dump()
                # 处理 datetime 字段
                for field_name, field_value in entity_data.items():
                    if hasattr(field_value, 'isoformat'):  # datetime 对象
                        entity_data[field_name] = field_value.isoformat()
                serializable_data[k] = entity_data

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving {file_path}: {e}")
    
    def save_draft(self, draft: DraftEntity):
        """保存草稿"""
        self._drafts[draft.id] = draft

        # 获取草稿文件夹路径
        folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
        draft_file = folder_path / "draft.json"

        # 保存草稿信息到独立文件夹
        self._save_single_data(draft_file, draft)
    
    def get_draft(self, draft_id: str) -> Optional[DraftEntity]:
        """获取草稿"""
        return self._drafts.get(draft_id)
    
    def get_all_drafts(self) -> List[DraftEntity]:
        """获取所有草稿"""
        return list(self._drafts.values())
    
    def save_video_segment(self, segment: VideoSegmentEntity):
        """保存视频片段"""
        self._video_segments[segment.id] = segment

        # 获取对应草稿的文件夹路径
        draft = self._drafts.get(segment.draft_id)
        if draft:
            folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
            segments_file = folder_path / "video_segments.json"

            # 获取该草稿的所有视频片段
            draft_segments = {k: v for k, v in self._video_segments.items() if v.draft_id == segment.draft_id}
            self._save_multiple_data(segments_file, draft_segments)
    
    def get_video_segments_by_draft(self, draft_id: str) -> List[VideoSegmentEntity]:
        """获取草稿的视频片段"""
        return [seg for seg in self._video_segments.values() if seg.draft_id == draft_id]
    
    def save_audio_segment(self, segment: AudioSegmentEntity):
        """保存音频片段"""
        self._audio_segments[segment.id] = segment

        # 获取对应草稿的文件夹路径
        draft = self._drafts.get(segment.draft_id)
        if draft:
            folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
            segments_file = folder_path / "audio_segments.json"

            # 获取该草稿的所有音频片段
            draft_segments = {k: v for k, v in self._audio_segments.items() if v.draft_id == segment.draft_id}
            self._save_multiple_data(segments_file, draft_segments)
    
    def get_audio_segments_by_draft(self, draft_id: str) -> List[AudioSegmentEntity]:
        """获取草稿的音频片段"""
        return [seg for seg in self._audio_segments.values() if seg.draft_id == draft_id]
    
    def save_text_segment(self, segment: TextSegmentEntity):
        """保存文本片段"""
        self._text_segments[segment.id] = segment

        # 获取对应草稿的文件夹路径
        draft = self._drafts.get(segment.draft_id)
        if draft:
            folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
            segments_file = folder_path / "text_segments.json"

            # 获取该草稿的所有文本片段
            draft_segments = {k: v for k, v in self._text_segments.items() if v.draft_id == segment.draft_id}
            self._save_multiple_data(segments_file, draft_segments)
    
    def get_text_segments_by_draft(self, draft_id: str) -> List[TextSegmentEntity]:
        """获取草稿的文本片段"""
        return [seg for seg in self._text_segments.values() if seg.draft_id == draft_id]
    
    def save_track(self, track: TrackEntity):
        """保存轨道"""
        self._tracks[track.id] = track

        # 获取对应草稿的文件夹路径
        draft = self._drafts.get(track.draft_id)
        if draft:
            folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
            tracks_file = folder_path / "tracks.json"

            # 获取该草稿的所有轨道
            draft_tracks = {k: v for k, v in self._tracks.items() if v.draft_id == track.draft_id}
            self._save_multiple_data(tracks_file, draft_tracks)
    
    def get_tracks_by_draft(self, draft_id: str) -> List[TrackEntity]:
        """获取草稿的轨道"""
        return [track for track in self._tracks.values() if track.draft_id == draft_id]
    
    def delete_track(self, track_id: str):
        """删除轨道"""
        if track_id in self._tracks:
            track = self._tracks[track_id]
            draft = self._drafts.get(track.draft_id)

            # 从内存中删除
            del self._tracks[track_id]

            # 更新对应草稿文件夹中的轨道文件
            if draft:
                folder_path = self._get_draft_folder_path(draft.draft_name, draft.id)
                tracks_file = folder_path / "tracks.json"

                # 获取该草稿的剩余轨道
                draft_tracks = {k: v for k, v in self._tracks.items() if v.draft_id == track.draft_id}
                self._save_multiple_data(tracks_file, draft_tracks)
    
    def get_segments_data(self) -> Dict[str, Any]:
        """获取所有片段数据（用于DraftUtils）"""
        return {
            "video_segments": self._video_segments,
            "audio_segments": self._audio_segments,
            "text_segments": self._text_segments
        }

    # ==================== 转场效果相关方法 ====================

    def save_transition(self, transition: TransitionEntity):
        """保存转场效果"""
        self._transitions[transition.id] = transition

        # 保存到文件
        draft = self.get_draft(transition.draft_id)
        if draft:
            draft_folder = self._get_draft_folder_path(draft.draft_name, draft.id)
            transitions_file = draft_folder / "transitions.json"

            # 获取该草稿的所有转场效果
            draft_transitions = {k: v for k, v in self._transitions.items() if v.draft_id == transition.draft_id}
            self._save_multiple_data(transitions_file, draft_transitions)

    def get_transitions_by_draft(self, draft_id: str) -> List[TransitionEntity]:
        """获取草稿的转场效果"""
        return [transition for transition in self._transitions.values() if transition.draft_id == draft_id]

    def delete_transition(self, transition_id: str):
        """删除转场效果"""
        if transition_id in self._transitions:
            transition = self._transitions[transition_id]
            draft_id = transition.draft_id

            # 从内存中删除
            del self._transitions[transition_id]

            # 更新文件
            draft = self.get_draft(draft_id)
            if draft:
                draft_folder = self._get_draft_folder_path(draft.draft_name, draft.id)
                transitions_file = draft_folder / "transitions.json"

                # 获取该草稿的剩余转场效果
                draft_transitions = {k: v for k, v in self._transitions.items() if v.draft_id == draft_id}
                self._save_multiple_data(transitions_file, draft_transitions)