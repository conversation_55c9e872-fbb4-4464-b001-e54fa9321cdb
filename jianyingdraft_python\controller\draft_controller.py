import io
import json
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.rep.draft_export_rep_dto import DraftExportRepDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.draft_export_req_dto import DraftExportReqDto
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/draft", tags=["草稿管理"])

# 使用集中式存储管理器
print("🔧 DraftController: 初始化服务...")
draft_service = DraftService()
draft_service.data_store = get_data_store()
print("✅ DraftController: 服务初始化完成")


@router.post("/create", response_model=DataResponse[DraftCreateRepDto])
async def create_draft(req_dto: DraftCreateReqDto):
    """创建草稿"""
    try:
        result = draft_service.create_draft_script(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export", response_model=DataResponse[DraftExportRepDto])
async def export_draft(req_dto: DraftExportReqDto):
    """导出草稿内容，返回draft_content.json和draft_meta_info.json"""
    try:
        # 创建脚本文件
        script = draft_service.create_script(
            draft_id=req_dto.draftId,
            width=req_dto.width,
            height=req_dto.height,
            fps=req_dto.fps
        )

        # 处理轨道数据
        draft_service.process_tracks(
            script=script,
            audio_tracks=req_dto.audioTracks,
            text_tracks=req_dto.textTracks,
            video_tracks=req_dto.videoTracks
        )

        # 导出脚本为JSON
        draft_content = draft_service.export_script_json(script)

        # 生成元数据信息
        draft_meta_info = draft_service.generate_meta_info(req_dto.draftId)

        # 构建响应
        result = DraftExportRepDto(
            draft_content=draft_content,
            draft_meta_info=draft_meta_info
        )

        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{draftId}/export")
async def export_draft_by_id(draftId: str):
    """根据草稿ID导出草稿为zip压缩包"""
    try:
        # 获取草稿信息
        draft_entity = draft_service.data_store.get_draft(draftId)
        if not draft_entity:
            raise SysException(404, f"未找到草稿: {draftId}")

        # 创建基本的脚本文件（没有轨道数据）
        script = draft_service.create_script(
            draft_id=draftId,
            width=draft_entity.width,
            height=draft_entity.height,
            fps=draft_entity.fps
        )

        # 导出脚本为JSON
        draft_content = draft_service.export_script_json(script)

        # 生成元数据信息
        draft_meta_info = draft_service.generate_meta_info(draftId, draft_entity)

        # 创建zip文件
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 添加draft_content.json
            zip_file.writestr(
                "draft_content.json",
                json.dumps(draft_content, ensure_ascii=False, indent=2)
            )

            # 添加draft_meta_info.json
            zip_file.writestr(
                "draft_meta_info.json",
                json.dumps(draft_meta_info, ensure_ascii=False, indent=2)
            )

        zip_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(zip_buffer.read()),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=draft_{draftId}.zip"}
        )
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



