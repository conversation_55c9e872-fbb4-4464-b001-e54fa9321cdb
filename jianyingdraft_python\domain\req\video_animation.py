from pydantic import BaseModel, Field
from typing import Optional
from ..req.resource import Resource


class VideoAnimation(BaseModel):
    """
    视频动画
    """
    type: Resource = Field(description="动画类型")
    duration: Optional[str] = Field(default=None, description="动画的时间")
    real_duration: Optional[int] = Field(default=None, description="动画的时间")

    def __init__(self, **data):
        super().__init__(**data)
        if self.duration is not None:
            from jianyingdraft_python.utils.time_utils import TimeUtils
            self.real_duration = TimeUtils.tim(self.duration)


class VideoAnimationReqDto(BaseModel):
    """
    视频动画请求参数
    """
    type: Resource = Field(description="动画类型")
    duration: Optional[str] = Field(default=None, description="动画的时间范围")
    draft_id: str = Field(description="草稿id")
    video_segment_id: str = Field(description="视频片段id")