from typing import Dict, Any
import uuid
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.req.text_animation_and_effect import TextAnimationAndEffectReqDto, TextAnimationAndEffect
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.draft_utils import DraftUtils
from jianyingdraft_python.utils.time_utils import TimeUtils
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.service.resource_validation_service import get_resource_validation_service


class TextSegmentService:
    """文本片段服务类"""

    def __init__(self):
        self.data_store = DataStore()

    def add_text_segment(self, req_dto: TextSegmentAddReqDto) -> TextSegmentEntity:
        """添加文本片段"""
        # 检查时间重叠
        segments_data = self.data_store.get_segments_data()
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetRanger,
            segment_type="text",
            exclude_segment_id=None,
            segments_data=segments_data
        )

        # 计算实际时间范围
        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetRanger,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration="5s",  # 文本默认时长
            segments_data=segments_data
        )

        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)

        segment_id = str(uuid.uuid4()).upper()
        segment = TextSegmentEntity(
            id=segment_id,
            content=req_dto.text,
            draft_id=req_dto.draftId,
            style=req_dto.style,
            border=req_dto.border,
            background=req_dto.background,
            target_timerange=target_timerange,
            real_target_timerange=real_target_timerange,
            track_id=req_dto.trackId
        )

        # 保存片段
        self.data_store.save_text_segment(segment)
        return segment

    def get_text_segments_by_draft(self, draft_id: str) -> list[TextSegmentEntity]:
        """获取草稿的文本片段"""
        return self.data_store.get_text_segments_by_draft(draft_id)

    def add_or_update_text_animation_and_effect_to_segment(self, req_dto: TextAnimationAndEffectReqDto) -> str:
        """给字幕片段添加或更新动画和特效"""
        # 资源验证
        resource_validator = get_resource_validation_service()

        if req_dto.type:
            validation_result = resource_validator.validate_resource(req_dto.type, "animations")
            if not validation_result["is_valid"]:
                print(f"⚠️ 文本动画资源验证警告: {validation_result['warning_message']}")

        # 获取文本片段
        segment = self._get_text_segment(req_dto.draft_id, req_dto.text_segment_id)

        # 创建动画和特效对象
        animation_and_effect = TextAnimationAndEffect(
            type=req_dto.type,
            duration=req_dto.duration,
            bubble_effect_id=req_dto.bubble_effect_id,
            bubble_resource_id=req_dto.bubble_resource_id,
            flower_effect_id=req_dto.flower_effect_id
        )

        # 设置动画和特效
        segment.animation_and_effect = animation_and_effect

        # 保存更新后的片段
        self.data_store.save_text_segment(segment)

        return req_dto.text_segment_id

    def _get_text_segment(self, draft_id: str, segment_id: str) -> TextSegmentEntity:
        """获取文本片段"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")

        # 获取文本片段
        segment = None
        for seg_id, seg in self.data_store._text_segments.items():
            if seg.id == segment_id and seg.draft_id == draft_id:
                segment = seg
                break

        if not segment:
            raise SysException.not_found("文本片段不存在")

        return segment