"""
视频蒙版请求DTO
"""
from pydantic import BaseModel, Field
from typing import Optional
from ..video.video_mask import VideoMask


class VideoMaskReqDto(BaseModel):
    """
    视频蒙版请求参数 - 完全匹配Kotlin版本
    """
    draft_id: str = Field(description="素材所属的 draftId", alias="draftId")
    segment_id: str = Field(description="素材所属的片段Id", alias="segmentId")
    mask: Optional[VideoMask] = Field(description="视频蒙版，一个片段只能有一个蒙版")

    class Config:
        allow_population_by_field_name = True
