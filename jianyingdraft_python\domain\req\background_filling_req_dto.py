"""
背景填充请求DTO - 完全匹配Kotlin版本
"""
from pydantic import BaseModel, Field


class BackgroundFillingReqDto(BaseModel):
    """
    背景填充请求参数 - 完全匹配Kotlin版本
    """
    draft_id: str = Field(description="草稿id", alias="draftId")
    video_segment_id: str = Field(description="视频片段id", alias="videoSegmentId")
    fill_type: str = Field(default="blur", description="背景填充类型", alias="fillType")
    blur: float = Field(default=0.625, description="模糊度, 0~1")
    color: str = Field(default="#00000000", description="填充颜色")

    class Config:
        allow_population_by_field_name = True
        json_schema_extra = {
            "example": {
                "draftId": "draft-123",
                "videoSegmentId": "segment-456",
                "fillType": "blur",
                "blur": 0.625,
                "color": "#00000000"
            }
        }
