"""
测试实际API的数据持久化功能
使用真实的数据目录和服务实例
"""
import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.track_controller import track_service
from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.controller.audio_segment_controller import audio_service
from jianyingdraft_python.controller.text_segment_controller import text_service
from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


def test_real_api_persistence():
    """测试真实API的数据持久化"""
    print("🔍 测试真实API的数据持久化...")
    
    # 获取真实的数据目录
    data_dir = os.path.join(project_root, "jianyingdraft_python", "data")
    print(f"📂 真实数据目录: {data_dir}")
    
    # 显示当前数据目录结构
    print("\n=== 当前数据目录结构 ===")
    show_directory_structure(data_dir)
    
    # 获取现有草稿
    print("\n=== 获取现有草稿 ===")
    try:
        drafts = draft_service.get_all_drafts()
        print(f"📊 现有草稿数量: {len(drafts)}")
        
        if not drafts:
            print("❌ 没有现有草稿，无法测试")
            return
        
        # 使用第一个草稿进行测试
        test_draft = drafts[0]
        draft_id = test_draft.id
        draft_name = test_draft.draft_name
        
        print(f"🎯 使用草稿进行测试:")
        print(f"   ID: {draft_id}")
        print(f"   名称: {draft_name}")
        
        # 确定草稿文件夹路径
        folder_name = draft_name if draft_name else draft_id
        # 清理文件夹名称
        import re
        folder_name = re.sub(r'[<>:"/\\|?*]', '_', folder_name)
        draft_folder = os.path.join(data_dir, folder_name)
        
        print(f"   文件夹: {draft_folder}")
        
        # 检查草稿文件夹是否存在
        if not os.path.exists(draft_folder):
            print(f"❌ 草稿文件夹不存在: {draft_folder}")
            return
        
        print(f"✅ 草稿文件夹存在")
        
        # 测试轨道API
        print(f"\n=== 测试轨道API ===")
        test_track_api(draft_id, draft_folder)
        
        # 获取轨道ID用于后续测试
        tracks = track_service.get_tracks_by_draft(draft_id)
        track_id = tracks[0].id if tracks else None
        
        if track_id:
            print(f"🎯 使用轨道ID: {track_id}")
            
            # 测试视频片段API
            print(f"\n=== 测试视频片段API ===")
            test_video_api(draft_id, track_id, draft_folder)
            
            # 测试音频片段API
            print(f"\n=== 测试音频片段API ===")
            test_audio_api(draft_id, track_id, draft_folder)
            
            # 测试文本片段API
            print(f"\n=== 测试文本片段API ===")
            test_text_api(draft_id, track_id, draft_folder)
        else:
            print("❌ 没有轨道，跳过片段测试")
        
        # 显示最终结构
        print(f"\n=== 测试后的数据目录结构 ===")
        show_directory_structure(data_dir)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_track_api(draft_id, draft_folder):
    """测试轨道API"""
    print("📝 调用轨道API...")
    
    # 检查服务实例的数据存储
    print(f"🔍 轨道服务数据存储目录: {track_service.data_store.data_dir}")
    
    track_req = TrackAddReqDto(
        draftId=draft_id,
        trackType="video",
        trackName="API测试轨道",
        mute=False,
        relativeIndex=0,
        absoluteIndex=1
    )
    
    try:
        result = track_service.add_track(track_req)
        track_id = result.id
        print(f"✅ 轨道API调用成功: {track_id}")
        
        # 检查文件是否创建/更新
        tracks_file = os.path.join(draft_folder, "tracks.json")
        print(f"🔍 检查轨道文件: {tracks_file}")
        
        if os.path.exists(tracks_file):
            print(f"✅ 轨道文件存在")
            
            # 读取文件内容
            with open(tracks_file, 'r', encoding='utf-8') as f:
                tracks_data = json.load(f)
            
            print(f"📄 轨道文件包含 {len(tracks_data)} 个轨道")
            
            if track_id in tracks_data:
                print(f"✅ 新轨道已保存到文件")
                track_data = tracks_data[track_id]
                print(f"   轨道名称: {track_data.get('track_name')}")
                print(f"   轨道类型: {track_data.get('track_type')}")
            else:
                print(f"❌ 新轨道未保存到文件")
                print(f"   文件中的轨道ID: {list(tracks_data.keys())}")
        else:
            print(f"❌ 轨道文件不存在")
            
    except Exception as e:
        print(f"❌ 轨道API调用失败: {e}")
        import traceback
        traceback.print_exc()


def test_video_api(draft_id, track_id, draft_folder):
    """测试视频片段API"""
    print("📝 调用视频片段API...")
    
    print(f"🔍 视频服务数据存储目录: {video_service.data_store.data_dir}")
    
    video_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/api_video.mp4",
        trackId=track_id
    )
    
    try:
        result = video_service.add_video_segment(video_req)
        segment_id = result.id
        print(f"✅ 视频片段API调用成功: {segment_id}")
        
        # 检查文件
        video_file = os.path.join(draft_folder, "video_segments.json")
        check_segment_file(video_file, segment_id, "视频片段")
        
    except Exception as e:
        print(f"❌ 视频片段API调用失败: {e}")
        import traceback
        traceback.print_exc()


def test_audio_api(draft_id, track_id, draft_folder):
    """测试音频片段API"""
    print("📝 调用音频片段API...")
    
    print(f"🔍 音频服务数据存储目录: {audio_service.data_store.data_dir}")
    
    audio_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/api_audio.mp3",
        trackId=track_id
    )
    
    try:
        result = audio_service.add_audio_segment(audio_req)
        segment_id = result.id
        print(f"✅ 音频片段API调用成功: {segment_id}")
        
        # 检查文件
        audio_file = os.path.join(draft_folder, "audio_segments.json")
        check_segment_file(audio_file, segment_id, "音频片段")
        
    except Exception as e:
        print(f"❌ 音频片段API调用失败: {e}")
        import traceback
        traceback.print_exc()


def test_text_api(draft_id, track_id, draft_folder):
    """测试文本片段API"""
    print("📝 调用文本片段API...")
    
    print(f"🔍 文本服务数据存储目录: {text_service.data_store.data_dir}")
    
    text_req = TextSegmentAddReqDto(
        draftId=draft_id,
        text="API测试文本",
        trackId=track_id,
        targetRanger=Timerange(start="0s", duration="3s")
    )
    
    try:
        result = text_service.add_text_segment(text_req)
        segment_id = result.id
        print(f"✅ 文本片段API调用成功: {segment_id}")
        
        # 检查文件
        text_file = os.path.join(draft_folder, "text_segments.json")
        check_segment_file(text_file, segment_id, "文本片段")
        
    except Exception as e:
        print(f"❌ 文本片段API调用失败: {e}")
        import traceback
        traceback.print_exc()


def check_segment_file(file_path, segment_id, segment_type):
    """检查片段文件"""
    print(f"🔍 检查{segment_type}文件: {file_path}")
    
    if os.path.exists(file_path):
        print(f"✅ {segment_type}文件存在")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            segments_data = json.load(f)
        
        print(f"📄 {segment_type}文件包含 {len(segments_data)} 个片段")
        
        if segment_id in segments_data:
            print(f"✅ 新{segment_type}已保存到文件")
        else:
            print(f"❌ 新{segment_type}未保存到文件")
            print(f"   文件中的片段ID: {list(segments_data.keys())}")
    else:
        print(f"❌ {segment_type}文件不存在")


def show_directory_structure(root_dir, prefix="", max_depth=2, current_depth=0):
    """显示目录结构"""
    if current_depth >= max_depth:
        return
        
    try:
        items = sorted(os.listdir(root_dir))
        for i, item in enumerate(items):
            item_path = os.path.join(root_dir, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(item_path):
                next_prefix = prefix + ("    " if is_last else "│   ")
                show_directory_structure(item_path, next_prefix, max_depth, current_depth + 1)
    except PermissionError:
        print(f"{prefix}[Permission Denied]")


if __name__ == "__main__":
    test_real_api_persistence()
