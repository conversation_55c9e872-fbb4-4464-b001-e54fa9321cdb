# 作者：Esther
# 剪映草稿API模块 - 提供ScriptFile相关的API接口
import logging
from typing import Optional, Dict, Any, List

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from api.draft_service import DraftService, DraftConfig

# 配置日志记录器
logger = logging.getLogger(__name__)


# ================== 数据模型 ==================
class DraftInfoRequest(BaseModel):
    """
    创建草稿请求模型
    
    Attributes:
        draftId: 草稿唯一标识符
        audioTracks: 音频轨道列表，可选
        videoTracks: 视频轨道列表，可选
        textTracks: 文本轨道列表，可选
        width: 视频宽度，默认1920
        height: 视频高度，默认1080
        fps: 帧率，默认30
    """
    draftId: str
    audioTracks: Optional[List[Dict[str, Any]]] = None
    videoTracks: Optional[List[Dict[str, Any]]] = None
    textTracks: Optional[List[Dict[str, Any]]] = None
    width: int = 1920
    height: int = 1080
    fps: int = 30


class DraftResponse(BaseModel):
    """
    草稿响应模型
    
    Attributes:
        success: 操作是否成功
        message: 响应消息
        data: 草稿数据，可选
    """
    code: int
    message: str
    data: Optional[Dict[str, Any]] = None


# ================== API接口层 ==================

# 创建草稿模块的路由器
router = APIRouter(prefix="/api/v1/drafts", tags=["草稿管理"])

# 初始化草稿服务实例
draft_service = DraftService()


@router.post("/export_script", response_model=DraftResponse)
async def export_script(request: DraftInfoRequest) -> DraftResponse:
    """
    创建并导出剪映草稿
    
    该接口接收草稿信息，创建剪映脚本文件，处理各种轨道数据，
    并返回完整的草稿JSON数据。
    
    Args:
        request: 创建草稿请求参数，包含草稿ID和轨道信息
        
    Returns:
        DraftResponse: 包含操作结果和草稿数据的响应对象
        
    Raises:
        HTTPException: 当请求数据无效或处理失败时抛出HTTP异常
    """
    try:
        logger.info(f"开始处理草稿导出请求，草稿ID: {request.draftId}")

        # 参数验证
        if not request.draftId:
            raise ValueError("草稿ID不能为空")

        # 创建脚本文件
        script = draft_service.create_script(
            draft_id=request.draftId,
            width=request.width,
            height=request.height,
            fps=request.fps
        )

        # 处理轨道数据
        draft_service.process_tracks(
            script=script,
            audio_tracks=request.audioTracks,
            text_tracks=request.textTracks,
            video_tracks=request.videoTracks
        )

        # 导出脚本为JSON
        script_data = draft_service.export_script_json(script)

        logger.info(f"草稿导出成功，草稿ID: {request.draftId}")

        return DraftResponse(
            code=200,
            message="草稿创建成功",
            data=script_data
        )

    except ValueError as ve:
        logger.error(f"参数验证错误: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))

    except Exception as e:
        logger.error(f"草稿导出失败: {e}")
        raise HTTPException(status_code=500, detail=f"草稿处理失败: {str(e)}")
