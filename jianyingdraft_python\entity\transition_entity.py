"""
转场效果实体
"""
from pydantic import BaseModel, Field
from datetime import datetime
from jianyingdraft_python.domain.req.transition_type import TransitionType


class TransitionEntity(BaseModel):
    """
    转场效果实体
    """
    id: str = Field(description="转场ID")
    draft_id: str = Field(description="草稿ID")
    from_segment_id: str = Field(description="起始片段ID")
    to_segment_id: str = Field(description="目标片段ID")
    transition_type: TransitionType = Field(description="转场类型")
    duration: str = Field(description="转场持续时间")
    track_id: str = Field(description="轨道ID")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间")
