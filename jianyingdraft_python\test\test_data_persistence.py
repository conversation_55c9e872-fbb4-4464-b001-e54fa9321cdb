"""
测试所有服务的数据持久化功能
验证接口调用后数据是否正确保存到文件系统
"""
import sys
import os
import tempfile
import shutil
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.service.track_service import TrackService
from jianyingdraft_python.service.video_segment_service import VideoSegmentService
from jianyingdraft_python.service.audio_segment_service import AudioSegmentService
from jianyingdraft_python.service.text_segment_service import TextSegmentService
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


class TestDataPersistence:
    """数据持久化测试类"""
    
    def __init__(self):
        # 创建临时数据目录
        self.temp_data_dir = tempfile.mkdtemp(prefix="test_persistence_")
        print(f"📂 临时测试目录: {self.temp_data_dir}")
        
        # 创建共享的数据存储实例
        self.data_store = DataStore(self.temp_data_dir)
        
        # 创建服务实例并使用共享的数据存储
        self.draft_service = DraftService()
        self.draft_service.data_store = self.data_store
        
        self.track_service = TrackService()
        self.track_service.data_store = self.data_store
        
        self.video_service = VideoSegmentService()
        self.video_service.data_store = self.data_store
        
        self.audio_service = AudioSegmentService()
        self.audio_service.data_store = self.data_store
        
        self.text_service = TextSegmentService()
        self.text_service.data_store = self.data_store
        
        self.draft_id = None
        self.draft_folder = None
    
    def cleanup(self):
        """清理测试数据"""
        try:
            shutil.rmtree(self.temp_data_dir)
            print(f"🧹 清理临时目录: {self.temp_data_dir}")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")
    
    def create_test_draft(self):
        """创建测试草稿"""
        print("\n=== 1. 创建测试草稿 ===")
        
        draft_req = DraftCreateReqDto(
            width=1920,
            height=1080,
            fps=30,
            name="持久化测试草稿",
            draftPath=os.path.join(self.temp_data_dir, "draft_output")
        )
        
        result = self.draft_service.create_draft_script(draft_req)
        self.draft_id = result.draftId
        self.draft_folder = os.path.join(self.temp_data_dir, "持久化测试草稿")
        
        print(f"✅ 草稿创建成功: {self.draft_id}")
        
        # 验证草稿文件是否创建
        draft_file = os.path.join(self.draft_folder, "draft.json")
        if os.path.exists(draft_file):
            print(f"✅ 草稿文件已创建: {draft_file}")
            return True
        else:
            print(f"❌ 草稿文件未创建: {draft_file}")
            return False
    
    def test_track_persistence(self):
        """测试轨道数据持久化"""
        print("\n=== 2. 测试轨道数据持久化 ===")
        
        # 添加轨道
        track_req = TrackAddReqDto(
            draftId=self.draft_id,
            trackType="video",
            trackName="主视频轨道",
            mute=False,
            relativeIndex=0,
            absoluteIndex=1
        )
        
        print(f"📝 添加轨道请求: {track_req.model_dump()}")
        
        try:
            result = self.track_service.add_track(track_req)
            track_id = result.id
            print(f"✅ 轨道添加成功: {track_id}")
            
            # 检查文件是否创建
            tracks_file = os.path.join(self.draft_folder, "tracks.json")
            if os.path.exists(tracks_file):
                print(f"✅ 轨道文件已创建: {tracks_file}")
                
                # 验证文件内容
                with open(tracks_file, 'r', encoding='utf-8') as f:
                    tracks_data = json.load(f)
                
                print(f"📄 轨道文件内容: {json.dumps(tracks_data, ensure_ascii=False, indent=2)}")
                
                if track_id in tracks_data:
                    print(f"✅ 轨道数据正确保存")
                    return True, track_id
                else:
                    print(f"❌ 轨道数据未找到")
                    return False, track_id
            else:
                print(f"❌ 轨道文件未创建: {tracks_file}")
                return False, None
                
        except Exception as e:
            print(f"❌ 轨道添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False, None
    
    def test_video_segment_persistence(self, track_id):
        """测试视频片段数据持久化"""
        print("\n=== 3. 测试视频片段数据持久化 ===")
        
        video_req = MediaSegmentAddReqDto(
            draftId=self.draft_id,
            targetTimerange=Timerange(start="0s", duration="5s"),
            resourcePath="/test/video.mp4",
            trackId=track_id
        )
        
        print(f"📝 添加视频片段请求: {video_req.model_dump()}")
        
        try:
            result = self.video_service.add_video_segment(video_req)
            segment_id = result.id
            print(f"✅ 视频片段添加成功: {segment_id}")
            
            # 检查文件是否创建
            video_file = os.path.join(self.draft_folder, "video_segments.json")
            if os.path.exists(video_file):
                print(f"✅ 视频片段文件已创建: {video_file}")
                
                # 验证文件内容
                with open(video_file, 'r', encoding='utf-8') as f:
                    video_data = json.load(f)
                
                print(f"📄 视频片段文件内容: {len(video_data)} 个片段")
                
                if segment_id in video_data:
                    print(f"✅ 视频片段数据正确保存")
                    return True
                else:
                    print(f"❌ 视频片段数据未找到")
                    return False
            else:
                print(f"❌ 视频片段文件未创建: {video_file}")
                return False
                
        except Exception as e:
            print(f"❌ 视频片段添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_audio_segment_persistence(self, track_id):
        """测试音频片段数据持久化"""
        print("\n=== 4. 测试音频片段数据持久化 ===")
        
        audio_req = MediaSegmentAddReqDto(
            draftId=self.draft_id,
            targetTimerange=Timerange(start="0s", duration="5s"),
            resourcePath="/test/audio.mp3",
            trackId=track_id
        )
        
        print(f"📝 添加音频片段请求: {audio_req.model_dump()}")
        
        try:
            result = self.audio_service.add_audio_segment(audio_req)
            segment_id = result.id
            print(f"✅ 音频片段添加成功: {segment_id}")
            
            # 检查文件是否创建
            audio_file = os.path.join(self.draft_folder, "audio_segments.json")
            if os.path.exists(audio_file):
                print(f"✅ 音频片段文件已创建: {audio_file}")
                
                # 验证文件内容
                with open(audio_file, 'r', encoding='utf-8') as f:
                    audio_data = json.load(f)
                
                print(f"📄 音频片段文件内容: {len(audio_data)} 个片段")
                
                if segment_id in audio_data:
                    print(f"✅ 音频片段数据正确保存")
                    return True
                else:
                    print(f"❌ 音频片段数据未找到")
                    return False
            else:
                print(f"❌ 音频片段文件未创建: {audio_file}")
                return False
                
        except Exception as e:
            print(f"❌ 音频片段添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_text_segment_persistence(self, track_id):
        """测试文本片段数据持久化"""
        print("\n=== 5. 测试文本片段数据持久化 ===")
        
        text_req = TextSegmentAddReqDto(
            draftId=self.draft_id,
            text="测试文本内容",
            trackId=track_id,
            targetRanger=Timerange(start="0s", duration="3s")
        )
        
        print(f"📝 添加文本片段请求: {text_req.model_dump()}")
        
        try:
            result = self.text_service.add_text_segment(text_req)
            segment_id = result.id
            print(f"✅ 文本片段添加成功: {segment_id}")
            
            # 检查文件是否创建
            text_file = os.path.join(self.draft_folder, "text_segments.json")
            if os.path.exists(text_file):
                print(f"✅ 文本片段文件已创建: {text_file}")
                
                # 验证文件内容
                with open(text_file, 'r', encoding='utf-8') as f:
                    text_data = json.load(f)
                
                print(f"📄 文本片段文件内容: {len(text_data)} 个片段")
                
                if segment_id in text_data:
                    print(f"✅ 文本片段数据正确保存")
                    return True
                else:
                    print(f"❌ 文本片段数据未找到")
                    return False
            else:
                print(f"❌ 文本片段文件未创建: {text_file}")
                return False
                
        except Exception as e:
            print(f"❌ 文本片段添加失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def show_final_structure(self):
        """显示最终的文件结构"""
        print("\n=== 6. 最终文件结构 ===")
        self._show_directory_tree(self.temp_data_dir)
    
    def _show_directory_tree(self, path, prefix="", max_depth=3, current_depth=0):
        """显示目录树"""
        if current_depth >= max_depth:
            return
            
        try:
            items = sorted(os.listdir(path))
            for i, item in enumerate(items):
                item_path = os.path.join(path, item)
                is_last = i == len(items) - 1
                
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item}")
                
                if os.path.isdir(item_path):
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    self._show_directory_tree(item_path, next_prefix, max_depth, current_depth + 1)
        except PermissionError:
            print(f"{prefix}[Permission Denied]")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试数据持久化功能...")
        
        results = {}
        
        try:
            # 1. 创建草稿
            results['draft'] = self.create_test_draft()
            
            if results['draft']:
                # 2. 测试轨道持久化
                track_success, track_id = self.test_track_persistence()
                results['track'] = track_success
                
                if track_success and track_id:
                    # 3. 测试各种片段持久化
                    results['video'] = self.test_video_segment_persistence(track_id)
                    results['audio'] = self.test_audio_segment_persistence(track_id)
                    results['text'] = self.test_text_segment_persistence(track_id)
                else:
                    results['video'] = False
                    results['audio'] = False
                    results['text'] = False
            
            # 显示最终结构
            self.show_final_structure()
            
            # 总结结果
            print("\n" + "=" * 60)
            print("🎯 数据持久化测试结果:")
            print(f"✅ 草稿创建: {'通过' if results.get('draft') else '失败'}")
            print(f"✅ 轨道保存: {'通过' if results.get('track') else '失败'}")
            print(f"✅ 视频片段保存: {'通过' if results.get('video') else '失败'}")
            print(f"✅ 音频片段保存: {'通过' if results.get('audio') else '失败'}")
            print(f"✅ 文本片段保存: {'通过' if results.get('text') else '失败'}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有数据持久化功能正常工作！")
            else:
                print("\n❌ 发现数据持久化问题，需要修复")
                
                # 分析问题
                failed_services = [k for k, v in results.items() if not v]
                print(f"🔍 失败的服务: {', '.join(failed_services)}")
            
            return results
            
        finally:
            self.cleanup()


if __name__ == "__main__":
    tester = TestDataPersistence()
    tester.run_all_tests()
