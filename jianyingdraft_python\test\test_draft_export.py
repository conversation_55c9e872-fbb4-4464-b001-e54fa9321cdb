import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch
from jianyingdraft_python.service.draft_service import DraftService, DraftConfig
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.domain.req.draft_export_req_dto import DraftExportReqDto
from jianyingdraft_python.domain.rep.draft_export_rep_dto import DraftExportRepDto
from jianyingdraft_python.storage.data_store import DataStore
from datetime import datetime


class TestDraftExport:
    """草稿导出功能测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.config = DraftConfig(width=1920, height=1080, fps=30)
        self.draft_service = DraftService(config=self.config)
        
        # 创建模拟的数据存储
        self.mock_data_store = Mock(spec=DataStore)
        self.draft_service.data_store = self.mock_data_store

    def test_create_script(self):
        """测试创建脚本功能"""
        draft_id = "TEST-DRAFT-001"
        width = 1920
        height = 1080
        fps = 30

        script = self.draft_service.create_script(
            draft_id=draft_id,
            width=width,
            height=height,
            fps=fps
        )

        assert script is not None
        assert script.content['id'] == draft_id
        assert script.content['width'] == width
        assert script.content['height'] == height
        assert script.content['fps'] == fps

    def test_create_script_with_defaults(self):
        """测试使用默认参数创建脚本"""
        draft_id = "TEST-DRAFT-002"

        script = self.draft_service.create_script(draft_id=draft_id)

        assert script is not None
        assert script.content['id'] == draft_id
        assert script.content['width'] == self.config.default_width
        assert script.content['height'] == self.config.default_height
        assert script.content['fps'] == self.config.default_fps

    def test_process_tracks(self):
        """测试处理轨道数据"""
        draft_id = "TEST-DRAFT-003"
        script = self.draft_service.create_script(draft_id=draft_id)

        # 准备测试数据
        audio_tracks = [
            {
                "trackName": "音频轨道1",
                "relativeIndex": 0,
                "absoluteIndex": 0,
                "mute": False,
                "audioSegments": [
                    {"id": "audio1", "start": 0, "duration": 5000}
                ]
            }
        ]

        text_tracks = [
            {
                "trackName": "文本轨道1",
                "relativeIndex": 0,
                "absoluteIndex": 1,
                "mute": False,
                "textSegments": [
                    {"id": "text1", "content": "测试文本", "start": 0, "duration": 3000}
                ]
            }
        ]

        video_tracks = [
            {
                "trackName": "视频轨道1",
                "relativeIndex": 0,
                "absoluteIndex": 2,
                "mute": False,
                "videoSegments": [
                    {"id": "video1", "path": "/test/video.mp4", "start": 0, "duration": 10000}
                ]
            }
        ]

        # 执行处理
        self.draft_service.process_tracks(
            script=script,
            audio_tracks=audio_tracks,
            text_tracks=text_tracks,
            video_tracks=video_tracks
        )

        # 验证处理结果（由于使用模拟对象，主要验证没有抛出异常）
        assert True  # 如果没有异常，测试通过

    def test_export_script_json(self):
        """测试导出脚本为JSON"""
        draft_id = "TEST-DRAFT-004"
        script = self.draft_service.create_script(draft_id=draft_id)

        result = self.draft_service.export_script_json(script)

        assert isinstance(result, dict)
        assert result['id'] == draft_id
        assert 'width' in result
        assert 'height' in result
        assert 'fps' in result

    def test_generate_meta_info_with_entity(self):
        """测试生成元数据信息（提供实体对象）"""
        draft_id = "TEST-DRAFT-005"
        draft_entity = DraftEntity(
            id=draft_id,
            draft_name="测试草稿",
            width=1920,
            height=1080,
            fps=30,
            draft_path="/test/path"
        )

        meta_info = self.draft_service.generate_meta_info(draft_id, draft_entity)

        assert isinstance(meta_info, dict)
        assert meta_info['draft_id'] == draft_id
        assert meta_info['draft_name'] == "测试草稿"
        assert meta_info['width'] == 1920
        assert meta_info['height'] == 1080
        assert meta_info['fps'] == 30
        assert meta_info['draft_path'] == "/test/path"
        assert 'export_time' in meta_info
        assert meta_info['version'] == "1.0"

    def test_generate_meta_info_from_store(self):
        """测试从数据存储生成元数据信息"""
        draft_id = "TEST-DRAFT-006"
        draft_entity = DraftEntity(
            id=draft_id,
            draft_name="存储测试草稿",
            width=1280,
            height=720,
            fps=25,
            draft_path="/store/path"
        )

        # 模拟数据存储返回
        self.mock_data_store.get_draft.return_value = draft_entity

        meta_info = self.draft_service.generate_meta_info(draft_id)

        assert isinstance(meta_info, dict)
        assert meta_info['draft_id'] == draft_id
        assert meta_info['draft_name'] == "存储测试草稿"
        self.mock_data_store.get_draft.assert_called_once_with(draft_id)

    def test_generate_meta_info_not_found(self):
        """测试草稿不存在时的元数据生成"""
        draft_id = "NONEXISTENT-DRAFT"
        
        # 模拟数据存储返回None
        self.mock_data_store.get_draft.return_value = None

        with pytest.raises(Exception):  # 应该抛出SysException，但这里用Exception代替
            self.draft_service.generate_meta_info(draft_id)

    def test_draft_export_req_dto(self):
        """测试导出请求DTO"""
        req_data = {
            "draftId": "TEST-DRAFT-007",
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "audioTracks": [
                {
                    "trackName": "音频轨道1",
                    "audioSegments": []
                }
            ],
            "textTracks": [],
            "videoTracks": []
        }

        req_dto = DraftExportReqDto(**req_data)

        assert req_dto.draftId == "TEST-DRAFT-007"
        assert req_dto.width == 1920
        assert req_dto.height == 1080
        assert req_dto.fps == 30
        assert len(req_dto.audioTracks) == 1
        assert len(req_dto.textTracks) == 0
        assert len(req_dto.videoTracks) == 0

    def test_draft_export_rep_dto(self):
        """测试导出响应DTO"""
        draft_content = {
            "id": "TEST-DRAFT-008",
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "tracks": []
        }

        draft_meta_info = {
            "draft_id": "TEST-DRAFT-008",
            "draft_name": "测试草稿",
            "version": "1.0"
        }

        rep_dto = DraftExportRepDto(
            draft_content=draft_content,
            draft_meta_info=draft_meta_info
        )

        assert rep_dto.draft_content['id'] == "TEST-DRAFT-008"
        assert rep_dto.draft_meta_info['draft_id'] == "TEST-DRAFT-008"
        assert rep_dto.draft_meta_info['version'] == "1.0"

    def test_full_export_workflow(self):
        """测试完整的导出工作流程"""
        draft_id = "TEST-DRAFT-009"
        
        # 创建脚本
        script = self.draft_service.create_script(
            draft_id=draft_id,
            width=1920,
            height=1080,
            fps=30
        )

        # 处理轨道（空轨道）
        self.draft_service.process_tracks(script=script)

        # 导出JSON
        draft_content = self.draft_service.export_script_json(script)

        # 生成元数据
        draft_entity = DraftEntity(
            id=draft_id,
            draft_name="完整测试草稿",
            width=1920,
            height=1080,
            fps=30,
            draft_path="/full/test/path"
        )
        draft_meta_info = self.draft_service.generate_meta_info(draft_id, draft_entity)

        # 验证结果
        assert draft_content['id'] == draft_id
        assert draft_meta_info['draft_id'] == draft_id
        assert draft_meta_info['draft_name'] == "完整测试草稿"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
