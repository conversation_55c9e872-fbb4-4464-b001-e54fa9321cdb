"""
验证修复后的草稿控制器是否正常工作
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.storage.data_store import DataStore


def test_get_all_drafts_fixed():
    """测试修复后的获取所有草稿功能"""
    print("🔧 测试修复后的获取所有草稿功能...")
    
    try:
        # 模拟控制器逻辑
        drafts = draft_service.get_all_drafts()
        print(f"📊 从数据存储获取到 {len(drafts)} 个草稿")
        
        # 测试修复后的DTO构建逻辑
        result = []
        for i, draft in enumerate(drafts):
            print(f"处理草稿 {i+1}: ID={draft.id}, 名称={draft.draft_name}")
            
            # 使用修复后的字段名
            dto = DraftCreateRepDto(
                draftId=draft.id,      # ✅ 修复后：使用正确的字段名
                draftPath=draft.draft_path  # ✅ 修复后：使用正确的字段名
            )
            result.append(dto)
            print(f"  ✅ DTO创建成功: {dto.model_dump()}")
        
        # 包装响应
        response = DataResponse.success(result)
        
        print(f"\n✅ 获取所有草稿功能修复成功！")
        print(f"📋 响应结果: {response.model_dump()}")
        return True
        
    except Exception as e:
        print(f"❌ 获取所有草稿功能仍有问题: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_get_draft_by_id_fixed():
    """测试修复后的根据ID获取草稿功能"""
    print("\n🔧 测试修复后的根据ID获取草稿功能...")
    
    # 先获取一个草稿ID
    drafts = draft_service.get_all_drafts()
    if not drafts:
        print("❌ 没有草稿可以测试")
        return False
    
    draft_id = drafts[0].id
    print(f"📝 测试草稿ID: {draft_id}")
    
    try:
        # 模拟控制器逻辑
        draft = draft_service.get_draft_by_id(draft_id)
        
        if not draft:
            print(f"❌ 草稿不存在")
            return False
        
        # 使用修复后的字段名
        result = DraftCreateRepDto(
            draftId=draft.id,      # ✅ 修复后：使用正确的字段名
            draftPath=draft.draft_path  # ✅ 修复后：使用正确的字段名
        )
        
        # 包装响应
        response = DataResponse.success(result)
        
        print(f"✅ 根据ID获取草稿功能修复成功！")
        print(f"📋 响应结果: {response.model_dump()}")
        return True
        
    except Exception as e:
        print(f"❌ 根据ID获取草稿功能仍有问题: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_data_storage_info():
    """显示数据存储信息"""
    print("\n📁 数据存储信息:")
    print("=" * 50)

    # 显示数据目录
    data_dir = os.path.join(project_root, "jianyingdraft_python", "data")
    print(f"📂 数据存储目录: {data_dir}")

    if os.path.exists(data_dir):
        items = os.listdir(data_dir)
        print(f"📄 数据文件和文件夹:")
        for item in items:
            item_path = os.path.join(data_dir, item)
            if os.path.isfile(item_path):
                size = os.path.getsize(item_path)
                print(f"  📄 {item} ({size} bytes)")
            elif os.path.isdir(item_path):
                print(f"  📁 {item}/")
                # 显示文件夹内容
                try:
                    sub_items = os.listdir(item_path)
                    for sub_item in sub_items:
                        print(f"     📄 {sub_item}")
                except:
                    pass
    else:
        print("❌ 数据目录不存在")

    # 显示当前草稿数据
    try:
        drafts = draft_service.get_all_drafts()
        print(f"\n📊 当前存储的草稿数量: {len(drafts)}")
        for i, draft in enumerate(drafts):
            print(f"  草稿 {i+1}:")
            print(f"    ID: {draft.id}")
            print(f"    名称: {draft.draft_name}")
            print(f"    路径: {draft.draft_path}")
            print(f"    创建时间: {draft.create_time}")
    except Exception as e:
        print(f"❌ 获取草稿数据失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始验证修复结果...")

    # 创建使用正确数据目录的服务实例
    data_dir = os.path.join(project_root, "jianyingdraft_python", "data")
    data_store = DataStore(data_dir)
    draft_service = DraftService()
    draft_service.data_store = data_store

    # 显示数据存储信息
    show_data_storage_info()

    # 测试修复后的功能
    test1_result = test_get_all_drafts_fixed()
    test2_result = test_get_draft_by_id_fixed()

    print("\n" + "=" * 50)
    print("🎯 修复验证结果:")
    print(f"✅ 获取所有草稿: {'通过' if test1_result else '失败'}")
    print(f"✅ 根据ID获取草稿: {'通过' if test2_result else '失败'}")

    if test1_result and test2_result:
        print("\n🎉 所有功能修复成功！现在可以正常调用 /api/draft/all 和 /api/draft/{draft_id} 接口了")
    else:
        print("\n❌ 仍有问题需要进一步修复")
