import pytest
import json
import os
from unittest.mock import Mock, patch
from jianyingdraft_python.service.draft_service import DraftService, DraftConfig
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.domain.rep.draft_export_rep_dto import DraftExportRepDto
from jianyingdraft_python.storage.data_store import DataStore
from datetime import datetime


class TestComprehensiveProjectExport:
    """综合API功能测试项目导出测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.config = DraftConfig(width=1920, height=1080, fps=30)
        self.draft_service = DraftService(config=self.config)

        # 设置测试数据路径
        self.test_data_path = r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目"

        # 创建模拟的数据存储
        self.mock_data_store = Mock(spec=DataStore)
        self.draft_service.data_store = self.mock_data_store

    def load_test_data(self):
        """加载测试数据"""
        # 加载草稿数据
        with open(os.path.join(self.test_data_path, "draft.json"), 'r', encoding='utf-8') as f:
            draft_data = json.load(f)

        # 加载轨道数据
        with open(os.path.join(self.test_data_path, "tracks.json"), 'r', encoding='utf-8') as f:
            tracks_data = json.load(f)

        # 加载音频片段数据
        with open(os.path.join(self.test_data_path, "audio_segments.json"), 'r', encoding='utf-8') as f:
            audio_segments_data = json.load(f)

        # 加载视频片段数据
        with open(os.path.join(self.test_data_path, "video_segments.json"), 'r', encoding='utf-8') as f:
            video_segments_data = json.load(f)

        return draft_data, tracks_data, audio_segments_data, video_segments_data

    def create_draft_entity_from_data(self, draft_data):
        """从测试数据创建草稿实体"""
        return DraftEntity(
            id=draft_data["id"],
            draft_name=draft_data["draft_name"],
            draft_content=draft_data["draft_content"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"],
            status=draft_data["status"],
            export_count=draft_data["export_count"],
            draft_path=draft_data["draft_path"],
            create_time=datetime.fromisoformat(draft_data["create_time"]),
            update_time=datetime.fromisoformat(draft_data["update_time"])
        )

    def save_export_results_to_folder(self, draft_content, draft_meta_info,
                                      output_folder=r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目\export_output"):
        """将导出结果保存到文件夹"""
        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)

        # 保存 draft_content.json
        draft_content_path = os.path.join(output_folder, "draft_content.json")
        with open(draft_content_path, 'w', encoding='utf-8') as f:
            json.dump(draft_content, f, ensure_ascii=False, indent=2)

        # 保存 draft_meta_info.json
        draft_meta_info_path = os.path.join(output_folder, "draft_meta_info.json")
        with open(draft_meta_info_path, 'w', encoding='utf-8') as f:
            json.dump(draft_meta_info, f, ensure_ascii=False, indent=2)

        print(f"✅ 导出文件已保存到: {output_folder}")
        print(f"   - draft_content.json: {draft_content_path}")
        print(f"   - draft_meta_info.json: {draft_meta_info_path}")

        return draft_content_path, draft_meta_info_path

    def organize_tracks_with_segments(self, tracks_data, audio_segments_data, video_segments_data):
        """组织轨道数据，包含对应的片段"""
        organized_tracks = []

        for track_id, track_data in tracks_data.items():
            track_with_segments = {
                "track_name": track_data["track_name"],
                "track_type": track_data["track_type"],
                "mute": track_data["mute"],
                "relative_index": track_data["relative_index"],
                "absolute_index": track_data["absolute_index"]
            }

            # 根据轨道类型添加对应的片段
            if track_data["track_type"] == "audio":
                audio_segments = []
                for segment_id, segment_data in audio_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        audio_segments.append(segment_data)
                track_with_segments["audio_segments"] = audio_segments

            elif track_data["track_type"] == "video":
                video_segments = []
                for segment_id, segment_data in video_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        video_segments.append(segment_data)
                track_with_segments["video_segments"] = video_segments

            elif track_data["track_type"] == "text":
                # 文本片段（如果有的话）
                track_with_segments["text_segments"] = []

            organized_tracks.append(track_with_segments)

        return organized_tracks

    def test_full_comprehensive_export_workflow(self):
        """测试完整的综合项目导出工作流程"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()

        # 1. 创建脚本
        script = self.draft_service.create_script(
            draft_id=draft_data["id"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"]
        )

        # 2. 组织轨道数据
        organized_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )

        audio_tracks = [track for track in organized_tracks if track["track_type"] == "audio"]
        video_tracks = [track for track in organized_tracks if track["track_type"] == "video"]
        text_tracks = [track for track in organized_tracks if track["track_type"] == "text"]

        # 3. 处理轨道
        self.draft_service.process_tracks(
            script=script,
            audio_tracks=audio_tracks,
            video_tracks=video_tracks,
            text_tracks=text_tracks
        )

        # 4. 导出草稿内容
        draft_content = self.draft_service.export_script_json(script)

        # 5. 生成元数据
        draft_entity = self.create_draft_entity_from_data(draft_data)
        draft_meta_info = self.draft_service.generate_meta_info(
            draft_id=draft_data["id"],
            draft_entity=draft_entity
        )

        # 6. 创建导出响应
        export_response = DraftExportRepDto(
            draft_content=draft_content,
            draft_meta_info=draft_meta_info
        )

        # 7. 保存导出结果到文件夹
        output_folder = r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目\export_output"
        draft_content_path, draft_meta_info_path = self.save_export_results_to_folder(
            draft_content, draft_meta_info, output_folder
        )

        # 验证完整导出结果
        assert export_response.draft_content['id'] == draft_data["id"]
        assert export_response.draft_meta_info['draft_id'] == draft_data["id"]
        assert export_response.draft_meta_info['draft_name'] == "综合API功能测试项目"

        # 验证文件已保存
        assert os.path.exists(draft_content_path)
        assert os.path.exists(draft_meta_info_path)

        # 验证保存的文件内容
        with open(draft_content_path, 'r', encoding='utf-8') as f:
            saved_draft_content = json.load(f)
        with open(draft_meta_info_path, 'r', encoding='utf-8') as f:
            saved_draft_meta_info = json.load(f)

        assert saved_draft_content['id'] == draft_data["id"]
        assert saved_draft_meta_info['draft_id'] == draft_data["id"]


if __name__ == "__main__":
    # 运行测试
    TestComprehensiveProjectExport().test_full_comprehensive_export_workflow()
