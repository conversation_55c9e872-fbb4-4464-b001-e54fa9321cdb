"""
视频滤镜请求DTO
"""
from pydantic import BaseModel, Field
from typing import List
from ..video.video_filter import VideoFilter


class VideoFilterReqDto(BaseModel):
    """
    视频滤镜请求参数 - 完全匹配Kotlin版本
    """
    draft_id: str = Field(description="素材所属的 draftId", alias="draftId")
    segment_id: str = Field(description="素材所属的片段Id", alias="segmentId")
    filters: List[VideoFilter] = Field(description="滤镜参数")

    class Config:
        allow_population_by_field_name = True
