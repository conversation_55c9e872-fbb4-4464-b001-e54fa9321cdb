"""
测试视频片段持续时间修复
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.controller.audio_segment_controller import audio_service
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


def test_duration_fix():
    """测试持续时间修复"""
    print("🔧 测试视频/音频片段持续时间修复...")
    
    # 使用您提供的输入数据
    req_data = {
        "draftId": "8280C669-8505-4B9E-8C9A-F242DA8552EA",
        "resourcePath": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\video.mp4",
        "speed": 1,
        "targetTimerange": {
            "duration": "5s",  # 期望的持续时间
            "start": "0s"
        },
        "trackId": "57CB3FA6-AEFB-4DCF-B5F7-7B25923AB206",
        "volume": 1
    }
    
    print(f"📝 输入数据: {req_data}")
    print(f"🎯 期望持续时间: {req_data['targetTimerange']['duration']}")
    
    # 1. 测试视频片段
    print(f"\n=== 测试视频片段 ===")
    
    try:
        timerange = Timerange(
            start=req_data["targetTimerange"]["start"],
            duration=req_data["targetTimerange"]["duration"]
        )
        
        req_dto = MediaSegmentAddReqDto(
            draftId=req_data["draftId"],
            resourcePath=req_data["resourcePath"],
            speed=req_data["speed"],
            targetTimerange=timerange,
            trackId=req_data["trackId"],
            volume=req_data["volume"]
        )
        
        result = video_service.add_video_segment(req_dto)
        
        print(f"✅ 视频片段添加成功: {result.id}")
        print(f"📊 输入持续时间: {req_data['targetTimerange']['duration']}")
        print(f"📊 输出持续时间: {result.target_timerange.duration}")
        
        if result.target_timerange.duration == req_data['targetTimerange']['duration']:
            print(f"✅ 持续时间正确保持")
        else:
            print(f"❌ 持续时间不匹配")
            print(f"   期望: {req_data['targetTimerange']['duration']}")
            print(f"   实际: {result.target_timerange.duration}")
        
    except Exception as e:
        print(f"❌ 视频片段测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 测试音频片段
    print(f"\n=== 测试音频片段 ===")
    
    try:
        # 修改资源路径为音频文件
        audio_req_data = req_data.copy()
        audio_req_data["resourcePath"] = "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\audio.mp3"
        audio_req_data["targetTimerange"]["duration"] = "3s"  # 使用不同的持续时间
        
        timerange = Timerange(
            start=audio_req_data["targetTimerange"]["start"],
            duration=audio_req_data["targetTimerange"]["duration"]
        )
        
        req_dto = MediaSegmentAddReqDto(
            draftId=audio_req_data["draftId"],
            resourcePath=audio_req_data["resourcePath"],
            speed=audio_req_data["speed"],
            targetTimerange=timerange,
            trackId=audio_req_data["trackId"],
            volume=audio_req_data["volume"]
        )
        
        result = audio_service.add_audio_segment(req_dto)
        
        print(f"✅ 音频片段添加成功: {result.id}")
        print(f"📊 输入持续时间: {audio_req_data['targetTimerange']['duration']}")
        print(f"📊 输出持续时间: {result.target_timerange.duration}")
        
        if result.target_timerange.duration == audio_req_data['targetTimerange']['duration']:
            print(f"✅ 持续时间正确保持")
        else:
            print(f"❌ 持续时间不匹配")
            print(f"   期望: {audio_req_data['targetTimerange']['duration']}")
            print(f"   实际: {result.target_timerange.duration}")
        
    except Exception as e:
        print(f"❌ 音频片段测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 检查文件内容
    print(f"\n=== 检查文件内容 ===")
    
    try:
        from jianyingdraft_python.config.app_config import get_app_config
        config = get_app_config()
        
        draft_folder = config.get_draft_folder_path("蹇伟", req_data["draftId"])
        
        # 检查视频片段文件
        video_file = draft_folder / "video_segments.json"
        if video_file.exists():
            import json
            with open(video_file, 'r', encoding='utf-8') as f:
                video_data = json.load(f)
            
            print(f"📄 视频片段文件包含 {len(video_data)} 个片段")
            
            # 显示最新的片段
            latest_video = list(video_data.values())[-1]
            print(f"   最新视频片段持续时间: {latest_video['target_timerange']['duration']}")
        
        # 检查音频片段文件
        audio_file = draft_folder / "audio_segments.json"
        if audio_file.exists():
            import json
            with open(audio_file, 'r', encoding='utf-8') as f:
                audio_data = json.load(f)
            
            print(f"📄 音频片段文件包含 {len(audio_data)} 个片段")
            
            # 显示最新的片段
            if audio_data:
                latest_audio = list(audio_data.values())[-1]
                print(f"   最新音频片段持续时间: {latest_audio['target_timerange']['duration']}")
        
    except Exception as e:
        print(f"❌ 检查文件内容失败: {e}")
    
    print(f"\n🎉 持续时间修复测试完成")


if __name__ == "__main__":
    test_duration_fix()
