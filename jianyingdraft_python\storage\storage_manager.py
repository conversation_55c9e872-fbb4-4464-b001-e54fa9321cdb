"""
统一的存储管理器
使用集中式配置，确保所有服务使用相同的数据存储实例
"""
from typing import Optional
from jianyingdraft_python.config.app_config import get_app_config
from jianyingdraft_python.storage.data_store import DataStore


class StorageManager:
    """存储管理器 - 单例模式"""
    
    _instance: Optional['StorageManager'] = None
    _data_store: Optional[DataStore] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._data_store is None:
            self._initialize_storage()
    
    def _initialize_storage(self):
        """初始化存储"""
        config = get_app_config()
        data_dir = config.data_directory
        
        print(f"🔧 StorageManager 初始化存储:")
        print(f"   使用数据目录: {data_dir}")
        
        self._data_store = DataStore(data_dir)
        
        print(f"✅ StorageManager 初始化完成")
    
    @property
    def data_store(self) -> DataStore:
        """获取数据存储实例"""
        if self._data_store is None:
            self._initialize_storage()
        return self._data_store
    
    def get_data_store(self) -> DataStore:
        """获取数据存储实例（方法形式）"""
        return self.data_store


# 全局存储管理器实例
storage_manager = StorageManager()


def get_data_store() -> DataStore:
    """获取全局数据存储实例"""
    return storage_manager.get_data_store()


def get_storage_manager() -> StorageManager:
    """获取存储管理器实例"""
    return storage_manager
