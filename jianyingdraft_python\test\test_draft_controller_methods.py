"""
测试 DraftController 的所有方法 - 验证输入输出参数和业务逻辑
"""
import sys
import os
import tempfile
import shutil
from typing import Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.rep.data_response import DataResponse


class TestDraftControllerMethods:
    """草稿控制器方法测试类"""
    
    def __init__(self):
        self.test_draft_ids = []  # 记录测试创建的草稿ID，用于清理
        self.temp_dirs = []  # 记录临时目录，用于清理
    
    def cleanup(self):
        """清理测试数据"""
        print("\n=== 清理测试数据 ===")
        # 清理临时目录
        for temp_dir in self.temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"✅ 清理临时目录: {temp_dir}")
                except Exception as e:
                    print(f"❌ 清理临时目录失败: {temp_dir}, 错误: {e}")
    
    def test_create_draft_controller_logic(self):
        """测试创建草稿控制器逻辑（不通过HTTP）"""
        print("\n=== 测试创建草稿控制器逻辑 ===")
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="test_controller_draft_")
        self.temp_dirs.append(temp_dir)
        
        # 测试正常创建草稿
        print("\n1. 测试正常创建草稿")
        req_dto = DraftCreateReqDto(
            width=1920,
            height=1080,
            fps=30,
            name="控制器测试草稿",
            draftPath=temp_dir
        )
        
        print(f"输入参数: {req_dto.model_dump()}")
        
        try:
            # 直接调用服务方法（模拟控制器逻辑）
            result = draft_service.create_draft_script(req_dto)
            response = DataResponse.success(result)
            
            print(f"✅ 创建草稿成功")
            print(f"服务返回类型: {type(result)}")
            print(f"服务返回结果: {result.model_dump()}")
            print(f"响应包装类型: {type(response)}")
            print(f"响应包装结果: {response.model_dump()}")
            
            # 验证返回结果的字段
            assert hasattr(result, 'draftId'), "返回结果缺少 draftId 字段"
            assert hasattr(result, 'draftPath'), "返回结果缺少 draftPath 字段"
            assert result.draftId is not None, "draftId 不能为空"
            assert result.draftPath == temp_dir, f"draftPath 不匹配，期望: {temp_dir}, 实际: {result.draftPath}"
            
            self.test_draft_ids.append(result.draftId)
            print(f"✅ 创建草稿控制器逻辑测试通过")
            return result.draftId
            
        except Exception as e:
            print(f"❌ 创建草稿失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def test_get_all_drafts_controller_logic(self):
        """测试获取所有草稿控制器逻辑"""
        print(f"\n=== 测试获取所有草稿控制器逻辑 ===")
        
        try:
            # 直接调用服务方法
            drafts = draft_service.get_all_drafts()
            
            # 模拟控制器逻辑：构建响应DTO
            print("🔍 测试控制器中的DTO构建逻辑...")
            result = []
            for draft in drafts:
                print(f"处理草稿: ID={draft.id}, 名称={draft.draft_name}")
                
                # 这里是控制器中的问题代码！
                try:
                    # 错误的字段名（当前控制器中的代码）
                    dto_wrong = DraftCreateRepDto(
                        draft_id=draft.id,  # ❌ 错误：应该是 draftId
                        draft_path=draft.draft_path  # ❌ 错误：应该是 draftPath
                    )
                    print(f"❌ 使用错误字段名创建DTO失败（这是预期的）")
                except Exception as e:
                    print(f"✅ 预期的错误：{e}")
                
                # 正确的字段名
                try:
                    dto_correct = DraftCreateRepDto(
                        draftId=draft.id,  # ✅ 正确
                        draftPath=draft.draft_path  # ✅ 正确
                    )
                    result.append(dto_correct)
                    print(f"✅ 使用正确字段名创建DTO成功: {dto_correct.model_dump()}")
                except Exception as e:
                    print(f"❌ 使用正确字段名创建DTO失败: {e}")
            
            # 包装响应
            response = DataResponse.success(result)
            
            print(f"✅ 获取所有草稿成功")
            print(f"草稿数量: {len(result)}")
            print(f"响应类型: {type(response)}")
            
            print(f"✅ 获取所有草稿控制器逻辑测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 获取所有草稿失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_get_draft_by_id_controller_logic(self, draft_id: str):
        """测试根据ID获取草稿控制器逻辑"""
        print(f"\n=== 测试根据ID获取草稿控制器逻辑 (ID: {draft_id}) ===")
        
        try:
            # 直接调用服务方法
            draft = draft_service.get_draft_by_id(draft_id)
            
            if not draft:
                print(f"❌ 草稿不存在")
                return False
            
            # 模拟控制器逻辑：构建响应DTO
            print("🔍 测试控制器中的DTO构建逻辑...")
            
            # 这里是控制器中的问题代码！
            try:
                # 错误的字段名（当前控制器中的代码）
                dto_wrong = DraftCreateRepDto(
                    draft_id=draft.id,  # ❌ 错误：应该是 draftId
                    draft_path=draft.draft_path  # ❌ 错误：应该是 draftPath
                )
                print(f"❌ 使用错误字段名创建DTO失败（这是预期的）")
            except Exception as e:
                print(f"✅ 预期的错误：{e}")
            
            # 正确的字段名
            try:
                dto_correct = DraftCreateRepDto(
                    draftId=draft.id,  # ✅ 正确
                    draftPath=draft.draft_path  # ✅ 正确
                )
                print(f"✅ 使用正确字段名创建DTO成功: {dto_correct.model_dump()}")
            except Exception as e:
                print(f"❌ 使用正确字段名创建DTO失败: {e}")
                return False
            
            # 包装响应
            response = DataResponse.success(dto_correct)
            
            print(f"✅ 根据ID获取草稿成功")
            print(f"响应类型: {type(response)}")
            print(f"响应结果: {response.model_dump()}")
            
            print(f"✅ 根据ID获取草稿控制器逻辑测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 根据ID获取草稿失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试 DraftController 的所有方法...")
        
        try:
            # 1. 测试创建草稿控制器逻辑
            draft_id = self.test_create_draft_controller_logic()
            
            # 2. 测试获取所有草稿控制器逻辑
            self.test_get_all_drafts_controller_logic()
            
            if draft_id:
                # 3. 测试根据ID获取草稿控制器逻辑
                self.test_get_draft_by_id_controller_logic(draft_id)
            
        finally:
            # 清理测试数据
            self.cleanup()
        
        print("\n🎉 所有控制器逻辑测试完成")
        print("\n📋 发现的问题总结：")
        print("1. ❌ 控制器中使用了错误的字段名：draft_id 应该是 draftId")
        print("2. ❌ 控制器中使用了错误的字段名：draft_path 应该是 draftPath")
        print("3. ✅ 服务层的方法工作正常")
        print("4. ✅ DTO类定义正确")


if __name__ == "__main__":
    tester = TestDraftControllerMethods()
    tester.run_all_tests()
