import pytest
import json
import os
from unittest.mock import Mock, patch
from jianyingdraft_python.service.draft_service import DraftService, DraftConfig
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.entity.track_entity import TrackEntity
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.domain.req.draft_export_req_dto import DraftExportReqDto
from jianyingdraft_python.domain.rep.draft_export_rep_dto import DraftExportRepDto
from jianyingdraft_python.storage.data_store import DataStore
from datetime import datetime


class TestComprehensiveProjectExport:
    """综合API功能测试项目导出测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.config = DraftConfig(width=1920, height=1080, fps=30)
        self.draft_service = DraftService(config=self.config)
        
        # 设置测试数据路径
        self.test_data_path = r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目"
        
        # 创建模拟的数据存储
        self.mock_data_store = Mock(spec=DataStore)
        self.draft_service.data_store = self.mock_data_store

    def load_test_data(self):
        """加载测试数据"""
        # 加载草稿数据
        with open(os.path.join(self.test_data_path, "draft.json"), 'r', encoding='utf-8') as f:
            draft_data = json.load(f)
        
        # 加载轨道数据
        with open(os.path.join(self.test_data_path, "tracks.json"), 'r', encoding='utf-8') as f:
            tracks_data = json.load(f)
        
        # 加载音频片段数据
        with open(os.path.join(self.test_data_path, "audio_segments.json"), 'r', encoding='utf-8') as f:
            audio_segments_data = json.load(f)
        
        # 加载视频片段数据
        with open(os.path.join(self.test_data_path, "video_segments.json"), 'r', encoding='utf-8') as f:
            video_segments_data = json.load(f)
        
        return draft_data, tracks_data, audio_segments_data, video_segments_data

    def create_draft_entity_from_data(self, draft_data):
        """从测试数据创建草稿实体"""
        return DraftEntity(
            id=draft_data["id"],
            draft_name=draft_data["draft_name"],
            draft_content=draft_data["draft_content"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"],
            status=draft_data["status"],
            export_count=draft_data["export_count"],
            draft_path=draft_data["draft_path"],
            create_time=datetime.fromisoformat(draft_data["create_time"]),
            update_time=datetime.fromisoformat(draft_data["update_time"])
        )

    def create_track_entities_from_data(self, tracks_data):
        """从测试数据创建轨道实体列表"""
        track_entities = []
        for track_id, track_data in tracks_data.items():
            track_entity = TrackEntity(
                id=track_data["id"],
                draft_id=track_data["draft_id"],
                track_type=track_data["track_type"],
                track_name=track_data["track_name"],
                mute=track_data["mute"],
                relative_index=track_data["relative_index"],
                absolute_index=track_data["absolute_index"],
                create_time=datetime.fromisoformat(track_data["create_time"]),
                update_time=datetime.fromisoformat(track_data["update_time"])
            )
            track_entities.append(track_entity)
        return track_entities

    def organize_tracks_with_segments(self, tracks_data, audio_segments_data, video_segments_data):
        """组织轨道数据，包含对应的片段"""
        organized_tracks = []
        
        for track_id, track_data in tracks_data.items():
            track_with_segments = {
                "track_name": track_data["track_name"],
                "track_type": track_data["track_type"],
                "mute": track_data["mute"],
                "relative_index": track_data["relative_index"],
                "absolute_index": track_data["absolute_index"]
            }
            
            # 根据轨道类型添加对应的片段
            if track_data["track_type"] == "audio":
                audio_segments = []
                for segment_id, segment_data in audio_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        audio_segments.append(segment_data)
                track_with_segments["audio_segments"] = audio_segments
                
            elif track_data["track_type"] == "video":
                video_segments = []
                for segment_id, segment_data in video_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        video_segments.append(segment_data)
                track_with_segments["video_segments"] = video_segments
                
            elif track_data["track_type"] == "text":
                # 文本片段（如果有的话）
                track_with_segments["text_segments"] = []
            
            organized_tracks.append(track_with_segments)
        
        return organized_tracks

    def test_load_comprehensive_test_data(self):
        """测试加载综合测试数据"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 验证数据加载成功
        assert draft_data["id"] == "9395E5FC-9456-40EB-883C-DA4720E6A5AE"
        assert draft_data["draft_name"] == "综合API功能测试项目"
        assert len(tracks_data) == 3  # 应该有3个轨道
        assert len(audio_segments_data) == 1  # 应该有1个音频片段
        assert len(video_segments_data) == 2  # 应该有2个视频片段

    def test_create_script_from_comprehensive_data(self):
        """测试从综合测试数据创建脚本"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 创建脚本
        script = self.draft_service.create_script(
            draft_id=draft_data["id"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"]
        )
        
        # 验证脚本创建成功
        assert script is not None
        assert script.content['id'] == draft_data["id"]
        assert script.content['width'] == draft_data["width"]
        assert script.content['height'] == draft_data["height"]
        assert script.content['fps'] == draft_data["fps"]

    def test_process_comprehensive_tracks(self):
        """测试处理综合测试数据的轨道"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 创建脚本
        script = self.draft_service.create_script(draft_id=draft_data["id"])
        
        # 组织轨道数据
        organized_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )
        
        # 分离不同类型的轨道
        audio_tracks = [track for track in organized_tracks if track["track_type"] == "audio"]
        video_tracks = [track for track in organized_tracks if track["track_type"] == "video"]
        text_tracks = [track for track in organized_tracks if track["track_type"] == "text"]
        
        # 处理轨道
        self.draft_service.process_tracks(
            script=script,
            audio_tracks=audio_tracks,
            video_tracks=video_tracks,
            text_tracks=text_tracks
        )
        
        # 验证处理成功（主要验证没有抛出异常）
        assert True

    def test_export_comprehensive_project_json(self):
        """测试导出综合测试项目为JSON"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 创建脚本
        script = self.draft_service.create_script(draft_id=draft_data["id"])
        
        # 组织并处理轨道数据
        organized_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )
        
        audio_tracks = [track for track in organized_tracks if track["track_type"] == "audio"]
        video_tracks = [track for track in organized_tracks if track["track_type"] == "video"]
        text_tracks = [track for track in organized_tracks if track["track_type"] == "text"]
        
        self.draft_service.process_tracks(
            script=script,
            audio_tracks=audio_tracks,
            video_tracks=video_tracks,
            text_tracks=text_tracks
        )
        
        # 导出为JSON
        exported_json = self.draft_service.export_script_json(script)
        
        # 验证导出结果
        assert isinstance(exported_json, dict)
        assert exported_json['id'] == draft_data["id"]
        assert 'width' in exported_json
        assert 'height' in exported_json
        assert 'fps' in exported_json

    def test_generate_meta_info_for_comprehensive_project(self):
        """测试为综合测试项目生成元数据"""
        draft_data, _, _, _ = self.load_test_data()
        
        # 创建草稿实体
        draft_entity = self.create_draft_entity_from_data(draft_data)
        
        # 生成元数据
        meta_info = self.draft_service.generate_meta_info(
            draft_id=draft_data["id"],
            draft_entity=draft_entity
        )
        
        # 验证元数据
        assert isinstance(meta_info, dict)
        assert meta_info['draft_id'] == draft_data["id"]
        assert meta_info['draft_name'] == draft_data["draft_name"]
        assert meta_info['width'] == draft_data["width"]
        assert meta_info['height'] == draft_data["height"]
        assert meta_info['fps'] == draft_data["fps"]
        assert meta_info['draft_path'] == draft_data["draft_path"]
        assert 'export_time' in meta_info
        assert meta_info['version'] == "1.0"

    def test_full_comprehensive_export_workflow(self):
        """测试完整的综合项目导出工作流程"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 1. 创建脚本
        script = self.draft_service.create_script(
            draft_id=draft_data["id"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"]
        )
        
        # 2. 组织轨道数据
        organized_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )
        
        audio_tracks = [track for track in organized_tracks if track["track_type"] == "audio"]
        video_tracks = [track for track in organized_tracks if track["track_type"] == "video"]
        text_tracks = [track for track in organized_tracks if track["track_type"] == "text"]
        
        # 3. 处理轨道
        self.draft_service.process_tracks(
            script=script,
            audio_tracks=audio_tracks,
            video_tracks=video_tracks,
            text_tracks=text_tracks
        )
        
        # 4. 导出草稿内容
        draft_content = self.draft_service.export_script_json(script)
        
        # 5. 生成元数据
        draft_entity = self.create_draft_entity_from_data(draft_data)
        draft_meta_info = self.draft_service.generate_meta_info(
            draft_id=draft_data["id"],
            draft_entity=draft_entity
        )
        
        # 6. 创建导出响应
        export_response = DraftExportRepDto(
            draft_content=draft_content,
            draft_meta_info=draft_meta_info
        )
        
        # 验证完整导出结果
        assert export_response.draft_content['id'] == draft_data["id"]
        assert export_response.draft_meta_info['draft_id'] == draft_data["id"]
        assert export_response.draft_meta_info['draft_name'] == "综合API功能测试项目"

    def test_create_export_request_dto_from_comprehensive_data(self):
        """测试从综合测试数据创建导出请求DTO"""
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 组织轨道数据
        organized_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )
        
        audio_tracks = [track for track in organized_tracks if track["track_type"] == "audio"]
        video_tracks = [track for track in organized_tracks if track["track_type"] == "video"]
        text_tracks = [track for track in organized_tracks if track["track_type"] == "text"]
        
        # 创建导出请求DTO
        export_request = DraftExportReqDto(
            draftId=draft_data["id"],
            width=draft_data["width"],
            height=draft_data["height"],
            fps=draft_data["fps"],
            audioTracks=audio_tracks,
            textTracks=text_tracks,
            videoTracks=video_tracks
        )
        
        # 验证请求DTO
        assert export_request.draftId == draft_data["id"]
        assert export_request.width == draft_data["width"]
        assert export_request.height == draft_data["height"]
        assert export_request.fps == draft_data["fps"]
        assert len(export_request.audioTracks) == 1
        assert len(export_request.videoTracks) == 1
        assert len(export_request.textTracks) == 1


if __name__ == "__main__":
    # 运行测试
    TestComprehensiveProjectExport().test_full_comprehensive_export_workflow()
