{"10B5FE54-A382-402F-B96B-CA8220BE9910": {"id": "10B5FE54-A382-402F-B96B-CA8220BE9910", "draft_id": "9395E5FC-9456-40EB-883C-DA4720E6A5AE", "target_timerange": {"start": "0s", "duration": "10s"}, "real_target_timerange": {"start": 0, "duration": 10000000}, "source_timerange": null, "real_source_timerange": null, "speed": 1.0, "volume": 0.8, "resource_path": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\video.mp4", "clip_settings": {"alpha": 1.0, "flip_horizontal": false, "flip_vertical": false, "rotation": 0.0, "scale_x": 1.0, "scale_y": 1.0, "transform_x": 0.0, "transform_y": 0.0}, "background_filling": {"fill_type": "blur", "blur": 0.7, "color": "#FF000080"}, "transition_type": {"transition_type": {"resource_id": "fade_transition", "resource_name": "淡入淡出转场"}, "duration": "1.5s", "real_duration": 1500000}, "animation": {"type": {"resource_id": "fade_in", "resource_name": "淡入动画"}, "duration": "2s", "real_duration": 2000000}, "video_effects": [{"effect_type": {"resource_id": "blur_effect", "resource_name": "模糊效果"}, "params": [30.0, 15.0]}, {"effect_type": {"resource_id": "glow_effect", "resource_name": "发光效果"}, "params": [50.0, 80.0, 20.0]}], "video_filters": [{"filter_type": {"resource_id": "vintage_filter", "resource_name": "复古滤镜"}, "intensity": 75.0}, {"filter_type": {"resource_id": "warm_filter", "resource_name": "暖色滤镜"}, "intensity": 60.0}], "video_mask": {"mask_type": {"resource_id": "circle_mask", "resource_name": "圆形蒙版"}, "center_x": 0.0, "center_y": 0.0, "size": 0.6, "rotation": 0.0, "feather": 0.15, "invert": false, "rect_width": null, "round_corner": null}, "track_id": "6874874F-45DC-403D-904A-614222C4E1C7", "media_info": {"file_name": "video.mp4", "absolute_path": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\video.mp4", "file_size": 0, "mime_type": "video/mp4", "type": "mp4", "width": 1920, "height": 1080, "duration_microseconds": 10000000, "duration_seconds": "10s"}, "create_time": "2025-08-09T20:26:53.672672", "update_time": "2025-08-09T20:26:53.672673"}, "AA96B07B-50F8-48FB-8801-371DFE7B5ED3": {"id": "AA96B07B-50F8-48FB-8801-371DFE7B5ED3", "draft_id": "9395E5FC-9456-40EB-883C-DA4720E6A5AE", "target_timerange": {"start": "12s", "duration": "3s"}, "real_target_timerange": {"start": 12000000, "duration": 3000000}, "source_timerange": null, "real_source_timerange": null, "speed": 1.2, "volume": 1.0, "resource_path": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\sticker.gif", "clip_settings": {"alpha": 1.0, "flip_horizontal": false, "flip_vertical": false, "rotation": 0.0, "scale_x": 1.0, "scale_y": 1.0, "transform_x": 0.0, "transform_y": 0.0}, "background_filling": null, "transition_type": null, "animation": null, "video_effects": [{"effect_type": {"resource_id": "sharpen_effect", "resource_name": "锐化效果"}, "params": [20.0]}], "video_filters": null, "video_mask": null, "track_id": "6874874F-45DC-403D-904A-614222C4E1C7", "media_info": {"file_name": "sticker.gif", "absolute_path": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\sticker.gif", "file_size": 0, "mime_type": "video/mp4", "type": "mp4", "width": 1920, "height": 1080, "duration_microseconds": 10000000, "duration_seconds": "10s"}, "create_time": "2025-08-09T20:26:53.682119", "update_time": "2025-08-09T20:26:53.682123"}}