from pydantic import BaseModel, Field
from typing import Optional
from ..req.resource import Resource


class TransitionType(BaseModel):
    """
    转场类型
    """
    transition_type: Resource = Field(description="转场类型")
    duration: Optional[str] = Field(default=None, description="转场持续时间")
    real_duration: Optional[int] = Field(default=None, description="动画的时间范围")

    def __init__(self, **data):
        super().__init__(**data)
        if self.duration is not None:
            from jianyingdraft_python.utils.time_utils import TimeUtils
            self.real_duration = TimeUtils.tim(self.duration)


class TransitionTypeReqDto(BaseModel):
    """
    转场特效请求参数
    """
    draft_id: str = Field(description="草稿id")
    video_segment_id: str = Field(description="视频片段id")
    transition_type: Resource = Field(description="转场类型")
    duration: Optional[str] = Field(default=None, description="转场持续时间")
    real_duration: int = Field(default=0, description="读取的转场持续时间")

    def __init__(self, **data):
        super().__init__(**data)
        if self.duration is not None:
            from jianyingdraft_python.utils.time_utils import TimeUtils
            self.real_duration = TimeUtils.tim(self.duration)