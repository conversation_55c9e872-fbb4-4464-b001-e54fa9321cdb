"""
转场效果请求DTO
"""
from pydantic import BaseModel, Field
from typing import Optional
from ..req.transition_type import TransitionType
from ..timerange import Timerange


class TransitionReqDto(BaseModel):
    """
    转场效果请求参数
    """
    id: Optional[str] = Field(default=None, description="转场ID，更新时需要")
    draft_id: str = Field(description="草稿ID")
    from_segment_id: str = Field(description="起始片段ID")
    to_segment_id: str = Field(description="目标片段ID")
    transition_type: TransitionType = Field(description="转场类型")
    duration: str = Field(description="转场持续时间，如'1s'")
    track_id: str = Field(description="轨道ID")
