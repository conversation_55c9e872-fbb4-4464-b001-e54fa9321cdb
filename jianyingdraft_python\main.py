from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging

# 导入控制器
from jianyingdraft_python.controller.draft_controller import router as draft_router
from jianyingdraft_python.controller.video_segment_controller import router as video_router
from jianyingdraft_python.controller.audio_segment_controller import router as audio_router
from jianyingdraft_python.controller.text_segment_controller import router as text_router
from jianyingdraft_python.controller.track_controller import router as track_router

# 导入其他控制器
from jianyingdraft_python.controller.effect_controller import router as effect_router
from jianyingdraft_python.controller.materials_utils_controller import router as materials_utils_router
from jianyingdraft_python.controller.resource_validation_controller import router as resource_validation_router

# 导入异常处理
from jianyingdraft_python.exception.sys_exception import SysException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="JianYing Draft Python API",
    description="剪映草稿Python版本API，完全照抄jianyingdraft_kotlin项目逻辑",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理
@app.exception_handler(SysException)
async def sys_exception_handler(request, exc: SysException):
    return JSONResponse(
        status_code=exc.code,
        content={"code": exc.code, "message": exc.message, "data": None}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"code": 500, "message": str(exc), "data": None}
    )

# 注册路由
app.include_router(draft_router)
app.include_router(video_router)
app.include_router(audio_router)
app.include_router(text_router)
app.include_router(track_router)

# 注册其他路由
app.include_router(effect_router)
app.include_router(materials_utils_router)
app.include_router(resource_validation_router)

# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "JianYing Draft Python API is running"}

# 根路径
@app.get("/")
async def root():
    return {
        "message": "欢迎使用JianYing Draft Python API",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )