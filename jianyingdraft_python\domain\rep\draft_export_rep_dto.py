from pydantic import BaseModel
from typing import Dict, Any


class DraftExportRepDto(BaseModel):
    """
    草稿导出响应DTO
    """
    draft_content: Dict[str, Any]
    draft_meta_info: Dict[str, Any]

    class Config:
        json_encoders = {
            # 可以在这里添加自定义编码器
        }
        schema_extra = {
            "example": {
                "draft_content": {
                    "id": "DRAFT-123456",
                    "width": 1920,
                    "height": 1080,
                    "fps": 30,
                    "tracks": []
                },
                "draft_meta_info": {
                    "draft_id": "DRAFT-123456",
                    "draft_name": "示例草稿",
                    "width": 1920,
                    "height": 1080,
                    "fps": 30,
                    "draft_path": "/path/to/draft",
                    "create_time": "2025-01-01T00:00:00",
                    "update_time": "2025-01-01T00:00:00",
                    "export_time": "2025-01-01T00:00:00",
                    "version": "1.0"
                }
            }
        }
