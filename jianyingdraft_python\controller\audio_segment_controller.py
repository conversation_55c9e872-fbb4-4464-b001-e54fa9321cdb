from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.audio_segment_service import AudioSegmentService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.resource import AudioFadeEffectReqDto, AudioKeyframeReqDto
from jianyingdraft_python.domain.req.audio_effect_req_dto import AudioEffectReqDto
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

# 使用Kotlin兼容的路径
router = APIRouter(prefix="/segment/audio", tags=["音频片段管理"])

# 使用集中式存储管理器
print("🔧 AudioController: 初始化服务...")
audio_service = AudioSegmentService()
audio_service.data_store = get_data_store()
print("✅ AudioController: 服务初始化完成")


@router.post("/add", response_model=DataResponse[AudioSegmentEntity])
async def add_audio_segment(req_dto: MediaSegmentAddReqDto):
    """添加音频片段"""
    try:
        result = audio_service.add_audio_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))




@router.post("/add-fade-effect", response_model=DataResponse[str])
async def add_audio_fade_effect(req_dto: AudioFadeEffectReqDto):
    """给音频片段添加或更新淡入淡出特效"""
    try:
        result = audio_service.add_audio_fade_effect(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-keyframe", response_model=DataResponse[str])
async def add_audio_keyframe(req_dto: AudioKeyframeReqDto):
    """给音频片段批量添加关键帧"""
    try:
        result = audio_service.add_audio_keyframe(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-effects", response_model=DataResponse[str])
async def add_audio_effects(req_dto: AudioEffectReqDto):
    """给音频片段添加特效"""
    try:
        result = audio_service.add_audio_effects(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))