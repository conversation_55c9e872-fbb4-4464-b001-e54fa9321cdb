from typing import Dict, Any
import uuid
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.video_animation import VideoAnimationReqDto, VideoAnimation
from jianyingdraft_python.domain.req.transition_type_req_dto import TransitionTypeReqDto
from jianyingdraft_python.domain.req.background_filling_req_dto import BackgroundFillingReqDto
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto
from jianyingdraft_python.domain.clip_settings import ClipSettings
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.draft_utils import DraftUtils
from jianyingdraft_python.utils.time_utils import TimeUtils
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.service.resource_validation_service import get_resource_validation_service


class VideoSegmentService:
    """视频片段服务类"""

    def __init__(self):
        self.data_store = DataStore()

    def add_video_segment(self, req_dto: MediaSegmentAddReqDto) -> VideoSegmentEntity:
        """添加视频片段"""
        # 检查时间重叠
        segments_data = self.data_store.get_segments_data()
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetTimerange,
            segment_type="video",
            exclude_segment_id=None,
            segments_data=segments_data
        )

        # 计算实际时间范围
        # 使用源时间范围的持续时间，如果没有则使用目标时间范围的持续时间
        calculated_duration = (
            req_dto.sourceTimerange.duration if req_dto.sourceTimerange
            else req_dto.targetTimerange.duration
        )

        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetTimerange,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration=calculated_duration,
            segments_data=segments_data
        )
        
        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)

        segment_id = str(uuid.uuid4()).upper()

        # 创建默认的 ClipSettings 和 MediaInfo
        clip_settings = req_dto.clipSettings if req_dto.clipSettings else ClipSettings()

        # 创建默认的 MediaInfo（从资源路径推断）
        import os
        file_name = os.path.basename(req_dto.resourcePath)
        media_info = MediaInfo(
            file_name=file_name,
            absolute_path=req_dto.resourcePath,
            file_size=0,  # 默认值，实际应该读取文件
            mime_type="video/mp4",  # 默认值
            type="mp4",  # 默认值
            width=1920,  # 默认值
            height=1080,  # 默认值
            duration_microseconds=10000000,  # 10秒，默认值
            duration_seconds="10s"  # 默认值
        )

        segment = VideoSegmentEntity(
            id=segment_id,
            draft_id=req_dto.draftId,
            target_timerange=target_timerange,
            real_target_timerange=real_target_timerange,
            source_timerange=req_dto.sourceTimerange,
            resource_path=req_dto.resourcePath,
            track_id=req_dto.trackId,
            media_info=media_info,
            clip_settings=clip_settings,
            speed=req_dto.speed,
            volume=req_dto.volume
        )

        # 保存片段
        self.data_store.save_video_segment(segment)
        return segment

    def get_video_segments_by_draft(self, draft_id: str) -> list[VideoSegmentEntity]:
        """获取草稿的视频片段"""
        return self.data_store.get_video_segments_by_draft(draft_id)

    def add_animation(self, req_dto: VideoAnimationReqDto) -> str:
        """给视频片段添加动画"""
        # 资源验证
        resource_validator = get_resource_validation_service()
        validation_result = resource_validator.validate_resource(req_dto.type, "animations")

        if not validation_result["is_valid"]:
            print(f"⚠️ 动画资源验证警告: {validation_result['warning_message']}")

        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.video_segment_id)

        # 创建动画对象
        animation = VideoAnimation(
            type=req_dto.type,
            duration=req_dto.duration
        )

        # 设置动画
        segment.animation = animation

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.video_segment_id

    def add_transition(self, req_dto: TransitionTypeReqDto) -> str:
        """给视频片段添加转场特效"""
        # 资源验证
        resource_validator = get_resource_validation_service()
        validation_result = resource_validator.validate_resource(req_dto.transition_type.transition_type, "transitions")

        if not validation_result["is_valid"]:
            print(f"⚠️ 转场资源验证警告: {validation_result['warning_message']}")

        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.video_segment_id)

        # 设置转场类型
        segment.transition_type = req_dto.transition_type

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.video_segment_id

    def add_background_filling(self, req_dto: BackgroundFillingReqDto) -> str:
        """给视频片段添加背景填充"""
        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.video_segment_id)

        # 创建BackgroundFilling对象，使用与Kotlin版本完全一致的参数
        from jianyingdraft_python.domain.req.background_filling import BackgroundFilling
        background_filling = BackgroundFilling(
            fill_type=req_dto.fill_type,
            blur=req_dto.blur,
            color=req_dto.color
        )
        segment.background_filling = background_filling

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.video_segment_id

    def add_effects(self, req_dto: VideoEffectReqDto) -> str:
        """给视频片段添加特效"""
        # 资源验证
        resource_validator = get_resource_validation_service()
        effect_resources = [effect.effect_type for effect in req_dto.effects]
        validation_result = resource_validator.validate_resources(effect_resources, "effects")

        if validation_result["warnings"]:
            print(f"⚠️ 特效资源验证警告: {'; '.join(validation_result['warnings'])}")

        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)

        # 添加特效（合并新特效到现有特效列表）
        if segment.video_effects is None:
            segment.video_effects = []

        # 根据resourceId去重，同一类型特效只能有一个
        existing_effect_types = {effect.effect_type.resource_id for effect in segment.video_effects}

        for new_effect in req_dto.effects:
            if new_effect.effect_type.resource_id not in existing_effect_types:
                segment.video_effects.append(new_effect)
            else:
                # 替换现有的同类型特效
                segment.video_effects = [
                    effect for effect in segment.video_effects
                    if effect.effect_type.resource_id != new_effect.effect_type.resource_id
                ]
                segment.video_effects.append(new_effect)

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.segment_id

    def add_filters(self, req_dto: VideoFilterReqDto) -> str:
        """给视频片段添加滤镜"""
        # 资源验证
        resource_validator = get_resource_validation_service()
        filter_resources = [filter.filter_type for filter in req_dto.filters]
        validation_result = resource_validator.validate_resources(filter_resources, "filters")

        if validation_result["warnings"]:
            print(f"⚠️ 滤镜资源验证警告: {'; '.join(validation_result['warnings'])}")

        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)

        # 添加滤镜（合并新滤镜到现有滤镜列表）
        if segment.video_filters is None:
            segment.video_filters = []

        # 根据resourceId去重，同一类型滤镜只能有一个
        existing_filter_types = {filter.filter_type.resource_id for filter in segment.video_filters}

        for new_filter in req_dto.filters:
            if new_filter.filter_type.resource_id not in existing_filter_types:
                segment.video_filters.append(new_filter)
            else:
                # 替换现有的同类型滤镜
                segment.video_filters = [
                    filter for filter in segment.video_filters
                    if filter.filter_type.resource_id != new_filter.filter_type.resource_id
                ]
                segment.video_filters.append(new_filter)

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.segment_id

    def add_mask(self, req_dto: VideoMaskReqDto) -> str:
        """给视频片段添加蒙版"""
        # 资源验证
        if req_dto.mask:
            resource_validator = get_resource_validation_service()
            validation_result = resource_validator.validate_resource(req_dto.mask.mask_type, "masks")

            if not validation_result["is_valid"]:
                print(f"⚠️ 蒙版资源验证警告: {validation_result['warning_message']}")

        # 获取视频片段
        segment = self._get_video_segment(req_dto.draft_id, req_dto.segment_id)

        # 设置蒙版（一个片段只能有一个蒙版，新蒙版会覆盖旧蒙版）
        segment.video_mask = req_dto.mask

        # 保存更新后的片段
        self.data_store.save_video_segment(segment)

        return req_dto.segment_id

    def _get_video_segment(self, draft_id: str, segment_id: str) -> VideoSegmentEntity:
        """获取视频片段"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")

        # 获取视频片段
        segment = None
        for seg_id, seg in self.data_store._video_segments.items():
            if seg.id == segment_id and seg.draft_id == draft_id:
                segment = seg
                break

        if not segment:
            raise SysException.not_found("视频片段不存在")

        return segment