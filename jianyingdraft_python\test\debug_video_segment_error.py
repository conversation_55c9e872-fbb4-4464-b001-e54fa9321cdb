"""
调试视频片段添加错误
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


def debug_video_segment_error():
    """调试视频片段错误"""
    print("🔍 调试视频片段添加错误...")
    
    # 使用您提供的输入数据
    req_data = {
        "draftId": "8280C669-8505-4B9E-8C9A-F242DA8552EA",
        "resourcePath": "D:\\PythonProject\\my-jianying\\readme_assets\\tutorial\\video.mp4",
        "speed": 1,
        "targetTimerange": {
            "duration": "5s",
            "start": "0s"
        },
        "trackId": "57CB3FA6-AEFB-4DCF-B5F7-7B25923AB206",
        "volume": 1
    }
    
    print(f"📝 输入数据: {req_data}")
    
    # 1. 检查文件是否存在
    video_file = req_data["resourcePath"]
    print(f"\n🔍 检查视频文件: {video_file}")
    
    if os.path.exists(video_file):
        file_size = os.path.getsize(video_file)
        print(f"✅ 文件存在，大小: {file_size} bytes")
    else:
        print(f"❌ 文件不存在")
    
    # 2. 检查草稿是否存在
    draft_id = req_data["draftId"]
    print(f"\n🔍 检查草稿: {draft_id}")
    
    try:
        from jianyingdraft_python.controller.draft_controller import draft_service
        draft = draft_service.get_draft_by_id(draft_id)
        if draft:
            print(f"✅ 草稿存在: {draft.draft_name}")
        else:
            print(f"❌ 草稿不存在")
            return
    except Exception as e:
        print(f"❌ 获取草稿失败: {e}")
        return
    
    # 3. 检查轨道是否存在
    track_id = req_data["trackId"]
    print(f"\n🔍 检查轨道: {track_id}")
    
    try:
        from jianyingdraft_python.controller.track_controller import track_service
        tracks = track_service.get_tracks_by_draft(draft_id)
        track_exists = any(track.id == track_id for track in tracks)
        if track_exists:
            print(f"✅ 轨道存在")
        else:
            print(f"❌ 轨道不存在")
            print(f"   现有轨道: {[track.id for track in tracks]}")
            return
    except Exception as e:
        print(f"❌ 获取轨道失败: {e}")
        return
    
    # 4. 尝试创建DTO
    print(f"\n🔍 创建MediaSegmentAddReqDto...")
    
    try:
        timerange = Timerange(
            start=req_data["targetTimerange"]["start"],
            duration=req_data["targetTimerange"]["duration"]
        )
        print(f"✅ Timerange创建成功: {timerange.model_dump()}")
        
        req_dto = MediaSegmentAddReqDto(
            draftId=req_data["draftId"],
            resourcePath=req_data["resourcePath"],
            speed=req_data["speed"],
            targetTimerange=timerange,
            trackId=req_data["trackId"],
            volume=req_data["volume"]
        )
        print(f"✅ MediaSegmentAddReqDto创建成功")
        print(f"   DTO内容: {req_dto.model_dump()}")
        
    except Exception as e:
        print(f"❌ 创建DTO失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 5. 尝试调用服务
    print(f"\n🔍 调用视频片段服务...")
    
    try:
        result = video_service.add_video_segment(req_dto)
        print(f"✅ 视频片段添加成功: {result.id}")
        print(f"   片段信息: {result.model_dump()}")
        
    except Exception as e:
        print(f"❌ 视频片段添加失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 详细分析错误
        print(f"\n🔍 错误详细分析:")
        print(f"   错误类型: {type(e).__name__}")
        print(f"   错误消息: {str(e)}")
        
        # 检查可能的问题
        analyze_potential_issues(req_dto)


def analyze_potential_issues(req_dto):
    """分析潜在问题"""
    print(f"\n🔍 分析潜在问题...")
    
    # 1. 检查数据存储
    print(f"1. 检查数据存储:")
    try:
        data_store = video_service.data_store
        print(f"   数据存储目录: {data_store.data_dir}")
        print(f"   内存中草稿数量: {len(data_store._drafts)}")
        print(f"   内存中轨道数量: {len(data_store._tracks)}")
        print(f"   内存中视频片段数量: {len(data_store._video_segments)}")
    except Exception as e:
        print(f"   ❌ 检查数据存储失败: {e}")
    
    # 2. 检查工具类方法
    print(f"\n2. 检查工具类方法:")
    try:
        from jianyingdraft_python.utils.draft_utils import DraftUtils
        segments_data = video_service.data_store.get_segments_data()
        print(f"   segments_data类型: {type(segments_data)}")
        print(f"   segments_data内容: {segments_data}")
        
        # 测试时间重叠检查
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetTimerange,
            segment_type="video",
            exclude_segment_id=None,
            segments_data=segments_data
        )
        print(f"   ✅ 时间重叠检查通过")
        
    except Exception as e:
        print(f"   ❌ 工具类方法失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 检查时间计算
    print(f"\n3. 检查时间计算:")
    try:
        from jianyingdraft_python.utils.time_utils import TimeUtils
        from jianyingdraft_python.utils.draft_utils import DraftUtils
        
        segments_data = video_service.data_store.get_segments_data()
        
        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetTimerange,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration=req_dto.sourceTimerange.duration if req_dto.sourceTimerange else "10s",
            segments_data=segments_data
        )
        print(f"   ✅ target_timerange: {target_timerange.model_dump()}")
        
        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)
        print(f"   ✅ real_target_timerange: {real_target_timerange.model_dump()}")
        
    except Exception as e:
        print(f"   ❌ 时间计算失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_video_segment_error()
