# 作者：Esther
# 剪映草稿服务类 - 提供草稿处理的核心业务逻辑
import json
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List

import pyJianYingDraft as draft
from api.transform_to_obj import convert_dict_to_object
from pyJianYingDraft import (
    AudioSegment, VideoSegment, ClipSettings, TextSegment, TextStyle, TextBorder,
    TextBackground, Timerange, AudioMaterial, VideoMaterial, CropSettings
)
from pyJianYingDraft.metadata.effect_meta import EffectEnum

# 配置日志记录器
logger = logging.getLogger(__name__)

# 常量定义
DEFAULT_FONT = {"resourceName": "文轩体", "resourceId": "7290445778273702455"}
DEFAULT_ANIMATION_DURATION = 500000
DEFAULT_VOLUME = 0.6
DEFAULT_SPEED = 1.0
DEFAULT_BLUR = 0.0625
DEFAULT_BACKGROUND_COLOR = "#00000000"


class DraftConfig:
    """
    草稿配置类 - 管理草稿的配置信息
    """

    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30) -> None:
        self.default_width = width
        self.default_height = height
        self.default_fps = fps


class TrackProcessor(ABC):
    """
    轨道处理器抽象基类
    """

    def __init__(self, config: DraftConfig) -> None:
        self.config = config

    @abstractmethod
    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        """处理轨道数据的抽象方法"""
        pass

    def add_track_to_script(self, script: draft.ScriptFile, track_data: Dict[str, Any],
                            track_type: draft.TrackType) -> None:
        """向脚本添加轨道"""
        try:
            track_name = track_data.get("trackName")
            script.add_track(
                track_type=track_type,
                track_name=track_name,
                relative_index=track_data.get("relativeIndex", 0),
                absolute_index=track_data.get("absoluteIndex"),
                mute=track_data.get("mute", False)
            )
            logger.info(f"成功添加{track_type.name}轨道: {track_name}")
        except Exception as e:
            logger.error(f"添加轨道失败: {e}")
            raise

    def _get_effect_from_data(self, effect_data: Dict[str, Any]) -> Optional[Any]:
        """从效果数据中获取效果对象"""
        resource_name = effect_data.get("resourceName")
        resource_id = effect_data.get("resourceId")
        return EffectEnum.find_effect_any_type(resource_name, resource_id)


class AudioTrackProcessor(TrackProcessor):
    """
    音频轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('trackName')
        logger.info(f"开始处理音频轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.audio)
        
        audio_segments = track_data.get('audioSegments')
        if audio_segments:
            self._process_audio_segments(script, track_name, audio_segments)

    def _process_audio_segments(self, script: draft.ScriptFile, track_name: str,
                                audio_segments: List[Dict[str, Any]]) -> None:
        """处理音频片段列表"""
        for segment_data in audio_segments:
            try:
                audio_segment = self._create_audio_segment(segment_data)
                self._add_all_effects(audio_segment, segment_data)
                script.add_segment(audio_segment, track_name=track_name)
                logger.info(f"成功添加音频片段到轨道: {track_name}")
            except Exception as e:
                logger.error(f"处理音频片段失败: {e}")
                raise

    def _add_all_effects(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加所有音频效果"""
        self._apply_audio_fade(audio_segment, segment_data)
        self._add_keyframes(audio_segment, segment_data)
        self._add_effects(audio_segment, segment_data)

    def _add_effects(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加音频效果"""
        audio_effects = segment_data.get('audioEffects', [])
        for effect_data in audio_effects:
            effect_type = effect_data.get('effectType')
            if effect_type:
                effect_meta = self._get_effect_from_data(effect_type)
                if effect_meta:
                    audio_segment.add_effect(effect_meta, effect_data.get('params'))
                    logger.info(f"添加音频片段效果: {effect_meta.value.name}")

    def _add_keyframes(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加音频关键帧"""
        keyframes = segment_data.get('audioKeyframes')
        if keyframes:
            for keyframe in keyframes:
                time_offset = keyframe.get('timeOffset')
                volume = keyframe.get('volume', 1.0)
                audio_segment.add_keyframe(time_offset, volume)
                logger.info(f"添加音频片段关键帧: {keyframe}")

    def _apply_audio_fade(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """应用音频淡入淡出效果"""
        fade_effect = segment_data.get('fadeEffect')
        if fade_effect:
            in_duration = fade_effect.get('inDuration')
            out_duration = fade_effect.get('outDuration')
            audio_segment.add_fade(in_duration, out_duration)
            logger.info("应用音频淡入淡出效果")

    def _create_audio_segment(self, segment_data: Dict[str, Any]) -> AudioSegment:
        """创建音频片段"""
        resource_path = segment_data.get('resourcePath')
        media_info = segment_data.get('mediaInfo', {})
        file_name = segment_data.get('fileName')
        
        audio_material = AudioMaterial(
            resource_path, 
            media_info.get('durationMicroseconds'), 
            file_name
        )
        
        return AudioSegment(
            material=audio_material,
            target_timerange=convert_dict_to_object(segment_data.get('realTargetTimerange'), Timerange),
            source_timerange=convert_dict_to_object(segment_data.get('sourceTimerange'), Timerange),
            speed=segment_data.get('speed', DEFAULT_SPEED),
            volume=segment_data.get('volume', DEFAULT_VOLUME)
        )


class TextTrackProcessor(TrackProcessor):
    """
    文本轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('trackName')
        logger.info(f"开始处理文本轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.text)
        
        text_segments = track_data.get('textSegments')
        if text_segments:
            self._process_text_segments(script, track_name, text_segments)

    def _process_text_segments(self, script: draft.ScriptFile, track_name: str,
                               text_segments: List[Dict[str, Any]]) -> None:
        """处理文本片段列表"""
        for segment_data in text_segments:
            text = segment_data.get('text')
            if text:
                try:
                    text_segment = self._create_text_segment(segment_data)
                    self._apply_text_animations_and_effects(text_segment, segment_data)
                    script.add_segment(text_segment, track_name=track_name)
                    logger.info(f"成功添加文本片段到轨道: {track_name}")
                except Exception as e:
                    logger.error(f"处理文本片段失败: {e}")
                    raise

    def _create_text_segment(self, segment_data: Dict[str, Any]) -> TextSegment:
        """创建文本片段"""
        font_data = segment_data.get('font', DEFAULT_FONT)
        font_effect = self._get_effect_from_data(font_data)
        
        return TextSegment(
            text=segment_data.get('text'),
            timerange=convert_dict_to_object(segment_data.get('realTargetRanger'), Timerange),
            font=font_effect,
            style=convert_dict_to_object(segment_data.get('style'), TextStyle),
            border=convert_dict_to_object(segment_data.get('border'), TextBorder),
            clip_settings=convert_dict_to_object(segment_data.get('clipSettings'), ClipSettings),
            background=convert_dict_to_object(segment_data.get('background'), TextBackground)
        )

    def _apply_text_animations_and_effects(self, text_segment: TextSegment, segment_data: Dict[str, Any]) -> None:
        """应用文本动画和效果"""
        text_animations = segment_data.get('textAnimationAndEffects')
        if text_animations:
            for animation_data in text_animations:
                self._apply_single_text_effect(text_segment, animation_data)

    def _apply_single_text_effect(self, text_segment: TextSegment, animation_data: Dict[str, Any]) -> None:
        """应用单个文本效果"""
        # 处理动画效果
        animation_type = animation_data.get("type")
        if animation_type:
            duration = animation_data.get("duration", DEFAULT_ANIMATION_DURATION)
            effect = self._get_effect_from_data(animation_type)
            if effect:
                text_segment.add_animation(effect, duration)
                logger.info("应用文本动画效果")

        # 处理气泡效果
        bubble_effect_id = animation_data.get("bubbleEffectId")
        bubble_resource_id = animation_data.get("bubbleResourceId")
        if bubble_effect_id and bubble_resource_id:
            text_segment.add_bubble(bubble_effect_id, bubble_resource_id)
            logger.info("应用文本气泡效果")

        # 处理花字效果
        flower_effect_id = animation_data.get("flowerEffectId")
        if flower_effect_id:
            text_segment.add_effect(flower_effect_id)
            logger.info("应用文本花字效果")


class VideoTrackProcessor(TrackProcessor):
    """
    视频轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('trackName')
        logger.info(f"开始处理视频轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.video)
        
        video_segments = track_data.get('videoSegments')
        if video_segments:
            self._process_video_segments(script, track_name, video_segments)

    def _process_video_segments(self, script: draft.ScriptFile, track_name: str,
                                video_segments: List[Dict[str, Any]]) -> None:
        """处理视频片段列表"""
        for segment_data in video_segments:
            try:
                video_segment = self._create_video_segment(segment_data)
                self._apply_video_effects(video_segment, segment_data)
                script.add_segment(video_segment, track_name=track_name)
                logger.info(f"成功添加视频片段到轨道: {track_name}")
            except Exception as e:
                logger.error(f"处理视频片段失败: {e}")
                raise

    def _create_video_segment(self, segment_data: Dict[str, Any]) -> VideoSegment:
        """创建视频片段"""
        resource_path = segment_data.get('resourcePath')
        media_info = segment_data.get('mediaInfo', {})
        
        video_material = VideoMaterial(
            path=resource_path,
            duration=media_info.get('durationMicroseconds'),
            height=media_info.get('height'),
            width=media_info.get('width'),
            material_name=media_info.get('fileName'),
            crop_settings=CropSettings(),
            material_type=segment_data.get('type', "video")
        )
        
        return VideoSegment(
            material=video_material,
            target_timerange=convert_dict_to_object(segment_data.get('realTargetTimerange'), Timerange),
            source_timerange=convert_dict_to_object(segment_data.get('sourceTimerange'), Timerange),
            speed=segment_data.get('speed', DEFAULT_SPEED),
            volume=segment_data.get('volume', DEFAULT_VOLUME),
            clip_settings=convert_dict_to_object(segment_data.get('clipSettings'), ClipSettings)
        )

    def _apply_video_effects(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用视频效果"""
        self._apply_transition_effect(video_segment, segment_data)
        self._apply_animation_effect(video_segment, segment_data)
        self._apply_background_filling(video_segment, segment_data)
        self._add_video_effect(video_segment, segment_data)
        self._add_filter(video_segment, segment_data)
        self._add_mask(video_segment, segment_data)

    def _add_mask(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加蒙版效果"""
        video_mask_data = segment_data.get('videoMask')
        if video_mask_data:
            mask_type = video_mask_data.get('maskType')
            mask_effect = self._get_effect_from_data(mask_type)
            if mask_effect:
                video_segment.add_mask(mask_effect,
                                       center_x=video_mask_data.get("centerX", 0.0),
                                       center_y=video_mask_data.get("centerY", 0.0),
                                       size=video_mask_data.get("size", 0.5),
                                       rotation=video_mask_data.get("rotation", 0.0),
                                       feather=video_mask_data.get("feather", 0.0),
                                       invert=video_mask_data.get("invert", False),
                                       rect_width=video_mask_data.get("rectWidth", None),
                                       round_corner=video_mask_data.get("roundCorner", None)
                                       )
                logger.info("应用视频蒙版效果")

    def _add_filter(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加滤镜效果"""
        video_filters_data = segment_data.get('videoFilters', [])
        for filter_data in video_filters_data:
            filter_type = filter_data.get('filterType', {})
            filter_effect = self._get_effect_from_data(filter_type)
            if filter_effect:
                video_segment.add_filter(filter_effect, filter_type.get("intensity"))
                logger.info("应用滤镜效果")

    def _add_video_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加视频特效"""
        video_effects_data = segment_data.get('videoEffects')
        if video_effects_data:
            for effect_data in video_effects_data:
                effect_type = effect_data.get('effectType', {})
                video_effect = self._get_effect_from_data(effect_type)
                if video_effect:
                    video_segment.add_effect(video_effect, effect_type.get("params"))
                    logger.info("应用视频特效")

    def _apply_transition_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用转场效果"""
        transition_data = segment_data.get('transitionType')
        if transition_data:
            effect_info = transition_data.get('transitionType')
            transition_effect = self._get_effect_from_data(effect_info)
            duration = transition_data.get('realDuration')
            if transition_effect:
                video_segment.add_transition(transition_type=transition_effect, duration=duration)
                logger.info("应用视频转场效果")

    def _apply_animation_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用动画效果"""
        animation_data = segment_data.get("animation")
        if animation_data:
            animation_type = animation_data.get('type')
            animation_effect = self._get_effect_from_data(animation_type)
            duration = animation_data.get('realDuration')
            if animation_effect:
                video_segment.add_animation(animation_type=animation_effect, duration=duration)
                logger.info("应用视频动画效果")

    def _apply_background_filling(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用背景填充效果"""
        background_data = segment_data.get("backgroundFilling")
        if background_data:
            fill_type = background_data.get("fillType")
            blur = background_data.get("blur", 0.0625)
            color = background_data.get("color", "#00000000")
            if fill_type:
                video_segment.add_background_filling(fill_type=fill_type, blur=blur, color=color)
                logger.info("应用视频背景填充")


class DraftService:
    """
    剪映草稿服务主类
    负责协调各种轨道处理器，创建和导出剪映脚本
    """

    def __init__(self, config: DraftConfig = None) -> None:
        """
        初始化草稿服务
        
        Args:
            config: 草稿配置对象，如果为None则使用默认配置
        """
        self.config = config or DraftConfig()
        self._initialize_processors()
        logger.info("草稿服务初始化完成")

    def _initialize_processors(self) -> None:
        """
        初始化各种轨道处理器
        """
        self.processors = {
            'audio': AudioTrackProcessor(self.config),
            'text': TextTrackProcessor(self.config),
            'video': VideoTrackProcessor(self.config)
        }
        logger.info("轨道处理器初始化完成")

    def create_script(self, draft_id: str, width: int = None, height: int = None, fps: int = None) -> draft.ScriptFile:
        """
        创建新的剪映脚本文件
        
        Args:
            draft_id: 草稿ID
            width: 视频宽度，如果为None则使用配置中的默认值
            height: 视频高度，如果为None则使用配置中的默认值
            fps: 帧率，如果为None则使用配置中的默认值
            
        Returns:
            创建的脚本文件对象
        """
        width = width or self.config.default_width
        height = height or self.config.default_height
        fps = fps or self.config.default_fps
        script = draft.ScriptFile(width=width, height=height, fps=fps)
        script.content['id'] = draft_id
        logger.info(f"创建新脚本文件，ID: {draft_id}, 分辨率: {width}x{height}, 帧率: {fps}")
        return script

    def process_tracks(self, script: draft.ScriptFile, audio_tracks: List[Dict[str, Any]] = None,
                       text_tracks: List[Dict[str, Any]] = None, video_tracks: List[Dict[str, Any]] = None) -> None:
        """
        处理所有轨道数据
        
        Args:
            script: 脚本文件对象
            audio_tracks: 音频轨道数据列表
            text_tracks: 文本轨道数据列表
            video_tracks: 视频轨道数据列表
        """
        logger.info("开始处理所有轨道数据")
        if audio_tracks:
            self._process_track_list(script, audio_tracks, 'audio')
        if text_tracks:
            self._process_track_list(script, text_tracks, 'text')
        if video_tracks:
            self._process_track_list(script, video_tracks, 'video')
        logger.info("所有轨道数据处理完成")

    def _process_track_list(self, script: draft.ScriptFile, tracks: List[Dict[str, Any]], track_type: str) -> None:
        """
        处理指定类型的轨道列表
        
        Args:
            script: 脚本文件对象
            tracks: 轨道数据列表
            track_type: 轨道类型
        """
        processor = self.processors.get(track_type)
        if not processor:
            logger.error(f"未找到轨道类型 {track_type} 的处理器")
            return
        for track_data in tracks:
            try:
                processor.process_track(script, track_data)
            except Exception as e:
                logger.error(f"处理 {track_type} 轨道失败: {e}")
                raise

    def export_script_json(self, script: draft.ScriptFile) -> Dict[str, Any]:
        """
        导出脚本为JSON格式
        
        Args:
            script: 脚本文件对象
            
        Returns:
            JSON格式的脚本字典
            
        Raises:
            Exception: 当导出失败时抛出异常
        """
        try:
            result = json.loads(script.dumps())
            logger.info("脚本导出为JSON格式成功")
            return result
        except Exception as e:
            logger.error(f"脚本导出失败: {e}")
            raise
