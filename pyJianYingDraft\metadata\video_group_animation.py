"""视频组合动画元数据"""

from .effect_meta import EffectEnum, AnimationMeta

class GroupAnimationType(EffectEnum):
    """视频组合动画类型"""

    # 免费特效
    三分割               = AnimationMeta("三分割", False, 0.500, "6873360856541827591", "922958", "558aba214155c31df287c64d9dc2b8e2")
    三分割_II            = AnimationMeta("三分割 II", False, 0.500, "6873360923646497293", "922957", "d7baaf62e894ef166fe291bb84332165")
    上下分割             = AnimationMeta("上下分割", False, 0.500, "6875935836177699335", "931224", "496fa5c31da1899de0cb0165138cd73f")
    上下分割_II          = AnimationMeta("上下分割 II", False, 0.500, "6875935919661126157", "3144548", "a6be02d01e9aa7277d023f063553543a")
    上升旋转             = AnimationMeta("上升旋转", False, 0.500, "6813965595915063815", "691841", "0cf52f154d94db3611b8dac8001fde2c")
    下降向右             = AnimationMeta("下降向右", False, 0.500, "6781683518222111239", "503140", "b5d536ab5791ef12d262982817b6ea0d")
    下降向左             = AnimationMeta("下降向左", False, 0.500, "6759351225772151303", "446392", "a1e5635da8aaa68df0eee59e202789dd")
    中间分割             = AnimationMeta("中间分割", False, 0.500, "6856970350270353928", "871868", "b2913d6efb446bda8d753430b8a13a82")
    中间分割_II          = AnimationMeta("中间分割 II", False, 0.500, "6856970411352003080", "871867", "3f62adfd71f2d308f59d4c3651fe5659")
    叠叠乐               = AnimationMeta("叠叠乐", False, 0.500, "6836319728038842894", "872824", "63c0c0634a0c5f99ec74f839421018b0")
    叠叠乐_II            = AnimationMeta("叠叠乐 II", False, 0.500, "6836319649844433415", "872826", "464c1a4bb1176029ffa3f1b6b7ae25be")
    叠叠乐_III           = AnimationMeta("叠叠乐 III", False, 0.500, "6836319781004513805", "872828", "8c55f6c4f5abf7d41e55a77fc8f5fdad")
    叠叠乐_IV            = AnimationMeta("叠叠乐 IV", False, 0.500, "6836319828656001550", "872830", "1b59e3f39c921c7b45f2cfa2daa6c71d")
    叠叠乐_V             = AnimationMeta("叠叠乐 V", False, 0.500, "6836319888827486728", "872834", "9d5c1a042586999b720b9c4582f15256")
    叠叠乐_Ⅵ             = AnimationMeta("叠叠乐 Ⅵ", False, 0.500, "6839582631345000967", "872836", "1c05ec01a8c9e8fc640a7b095ccf361d")
    右拉镜               = AnimationMeta("右拉镜", False, 0.500, "6772415374165021191", "471347", "1034060f611109671482d36f4d31fb17")
    向右下降             = AnimationMeta("向右下降", False, 0.500, "6781683438396117517", "503138", "3076ad170eb3bfc352c857e588defb4c")
    向右缩小             = AnimationMeta("向右缩小", False, 0.500, "6772415063216099848", "471341", "f5995769a9513563c0417cfe9b916183")
    向左下降             = AnimationMeta("向左下降", False, 0.500, "6760223716392571395", "447588", "9ba3825d7883b956d5e73f43f59db545")
    向左缩小             = AnimationMeta("向左缩小", False, 0.500, "6772415148423385607", "471343", "5c2a1617a5aa2f274bc12be4a41eed88")
    哈哈镜               = AnimationMeta("哈哈镜", False, 0.500, "6832226792556728846", "748348", "d6586e3b2806e4f4930ac34624c2967c")
    哈哈镜_II            = AnimationMeta("哈哈镜 II", False, 0.500, "6832226909875606029", "748350", "8eae60bc0b8c8d210f9f4b059c24ab64")
    四格滑动             = AnimationMeta("四格滑动", False, 0.500, "6883727868451361293", "945730", "1e8fc914a15209bc51eb0505f2e67816")
    四格翻转             = AnimationMeta("四格翻转", False, 0.500, "6865578846393995784", "1362932", "88c1ed96cfa3f182a341c27adc7edfdb")
    四格转动             = AnimationMeta("四格转动", False, 0.500, "6891835548688716302", "957940", "c849845a30f5df0c3b7f4b1620b5c248")
    四格转动_II          = AnimationMeta("四格转动 II", False, 0.500, "6891835601067184653", "957939", "087665cb644cb25f0ffbc99c8cb18237")
    回弹伸缩             = AnimationMeta("回弹伸缩", False, 0.500, "6795425591014199822", "530249", "2b125e477d54adb475d9834eb209f8c1")
    夹心饼干             = AnimationMeta("夹心饼干", False, 0.500, "6868146033247916558", "1362936", "23091e7d56610c4253a186488657cd30")
    夹心饼干_II          = AnimationMeta("夹心饼干 II", False, 0.500, "6868146123710665223", "1362934", "09b29b56757a17ad7ca31a57eb8b5726")
    小火车               = AnimationMeta("小火车", False, 0.500, "6860405888784536072", "885144", "b302925aabfc7ad44b7ff56cf3eeb1af")
    小火车_II            = AnimationMeta("小火车 II", False, 0.500, "6860406007160377863", "885143", "dc3796c0b273ece45e7b919f4e95b13e")
    小火车_III           = AnimationMeta("小火车 III", False, 0.500, "6860406091700769293", "885142", "665257a20f1d22af2ec8d5a22c57c35c")
    小火车_IV            = AnimationMeta("小火车 IV", False, 0.500, "6860406196130550286", "885141", "44d91b6dc8026eb108a2d58de508605a")
    小陀螺               = AnimationMeta("小陀螺", False, 0.500, "6874487656969933325", "923592", "a2ca54bf1fe5f095558e59d119f89dbb")
    小陀螺_II            = AnimationMeta("小陀螺 II", False, 0.500, "6874487735059485198", "923591", "decf2dddea3ff279910af80c73ec7e9e")
    左右分割             = AnimationMeta("左右分割", False, 0.500, "6886282872680878599", "948476", "2950fd3e74e3e251a83de19f46d12072")
    左右分割_II          = AnimationMeta("左右分割 II", False, 0.500, "6886282936048423431", "948475", "43bd89b22b945308fe5b7f722cf68c09")
    左拉镜               = AnimationMeta("左拉镜", False, 0.500, "6772415248973435395", "471345", "81d1a0c1b959c9910f775497cf55cffa")
    弹入旋转             = AnimationMeta("弹入旋转", False, 0.500, "6810286558826992136", "669963", "03a824f3bfa5b694a0ccbb8e83cc8c93")
    形变右缩             = AnimationMeta("形变右缩", False, 0.500, "6851395907804467720", "813139", "9add6fd7e0ee139401cb703b95bb307d")
    形变左缩             = AnimationMeta("形变左缩", False, 0.500, "6851395726937690637", "813140", "114b8e787f082f3fec108ee4b3f1be18")
    形变缩小             = AnimationMeta("形变缩小", False, 0.500, "6777260789263766030", "487587", "307f8bcc9b47ed8c20e5ac4822bb4659")
    悠悠球               = AnimationMeta("悠悠球", False, 0.500, "6821451358101574152", "717346", "7f09519094e625c0af9868f528821ad1")
    悠悠球_II            = AnimationMeta("悠悠球 II", False, 0.500, "6821451462904648200", "717348", "5505c62331247b24a5ce39730bcb9051")
    手机                 = AnimationMeta("手机", False, 0.500, "6861892418334102030", "1362928", "55fd386ec6f779110e3b12b89deaa79c")
    手机_II              = AnimationMeta("手机 II", False, 0.500, "6862918279183208973", "1362926", "b861f8270870663e5c3f8bbb1fced93a")
    手机_III             = AnimationMeta("手机 III", False, 0.500, "6862918366550561294", "1362924", "e056d0f8601d91d70e20e61bd63793f5")
    扭曲拉伸             = AnimationMeta("扭曲拉伸", False, 0.500, "7026278592623415822", "1426278", "ca0a43e525601adf9d14089393610d9c")
    抖入放大             = AnimationMeta("抖入放大", False, 0.500, "6761360765925462536", "450264", "dcf80421b0f2682c13b062d0c599aac4")
    拉伸扭曲             = AnimationMeta("拉伸扭曲", False, 0.500, "7025952723027628557", "1425496", "39862f9fa5c934d72d36923f2368de4a")
    放大弹动             = AnimationMeta("放大弹动", False, 0.500, "7023931891363353101", "1418682", "8ba074c2bba2e21717e17f4e80fb80d7")
    斜转                 = AnimationMeta("斜转", False, 0.500, "6847734302193488392", "872874", "4cc2597d961bafbd3c7877bc3f75f79a")
    斜转_II              = AnimationMeta("斜转 II", False, 0.500, "6847734360636920327", "872876", "b37e163c186d88db144aacd6280c0811")
    方片转动             = AnimationMeta("方片转动", False, 0.500, "6897114113726485000", "968162", "dfc6082d56863c5e700a0a5a17102abc")
    方片转动_II          = AnimationMeta("方片转动 II", False, 0.500, "6897114201702011405", "968161", "afdb5e78ddf7d480e78cabb527db9241")
    旋入晃动             = AnimationMeta("旋入晃动", False, 0.500, "6789167874511475207", "519840", "f791a333e80fb4d7c2646c394adc2d25")
    旋出渐隐             = AnimationMeta("旋出渐隐", False, 0.500, "6824302025698710024", "719940", "40cee4a8c1457ab5e4f15645532fd23e")
    旋转上升             = AnimationMeta("旋转上升", False, 0.500, "6813965670716281352", "691843", "982059c400ca42676d02f9847d281e1e")
    旋转伸缩             = AnimationMeta("旋转伸缩", False, 0.500, "6795425422046663182", "530247", "2c211882b63c7115a7c687faf3c79237")
    旋转回吸             = AnimationMeta("旋转回吸", False, 0.500, "6810286613898203661", "669965", "57c2c2e50d57bc5ea10b8c32f8e850cb")
    旋转缩小             = AnimationMeta("旋转缩小", False, 0.500, "6759046644462785037", "445858", "a6749bbbebb7a8fb3ad27679989b7401")
    旋转降落             = AnimationMeta("旋转降落", False, 0.500, "6759046515521491464", "445856", "7c87f107b2dbc7d9abe648f4f6e86e58")
    晃动旋出             = AnimationMeta("晃动旋出", False, 0.500, "6789167998700622350", "519842", "49fa06c52e99c0fe974343b4da58e94c")
    水晶                 = AnimationMeta("水晶", False, 0.500, "6857333749718192654", "1362920", "de28fd5ff1c5fa607cc09306a5de1fc9")
    水晶_II              = AnimationMeta("水晶 II", False, 0.500, "6857333869541069325", "1362922", "66d0f40fad32ba15e72d6687f12604f8")
    波动滑出             = AnimationMeta("波动滑出", False, 0.500, "7017646605671076359", "1392376", "c546f43ce65a4977ee11010063ee7b50")
    海盗船               = AnimationMeta("海盗船", False, 0.500, "6830302168751280648", "1362866", "a5e464c4bb091e8e43077aa05a1b20a8")
    海盗船_II            = AnimationMeta("海盗船 II", False, 0.500, "6830302282995732999", "1362868", "e4b5dc8efea09d3b0f0a7f8e780e489c")
    海盗船_III           = AnimationMeta("海盗船 III", False, 0.500, "6830302335047045639", "1362872", "59258d3cbc5c09058d95ee8721dd83f4")
    海盗船_IV            = AnimationMeta("海盗船 IV", False, 0.500, "6830302424826122765", "1362870", "fa532cb7a9d382c56927facf5cea657b")
    滑入波动             = AnimationMeta("滑入波动", False, 0.500, "7023747922718102023", "1418546", "7c4a80c235da2050b672f66a0a9e54b3")
    滑滑梯               = AnimationMeta("滑滑梯", False, 0.500, "6828829568879563271", "741020", "4727fafafc252616d70342eac1117dea")
    滑滑梯_II            = AnimationMeta("滑滑梯 II", False, 0.500, "6828829741013799432", "741022", "1ae8aa09ca773e05cd96aed6569e49a1")
    百叶窗               = AnimationMeta("百叶窗", False, 0.500, "6771299961171612174", "467361", "f4cf50145a6c09ce363ca7d49ecbc05f")
    百叶窗_II            = AnimationMeta("百叶窗 II", False, 0.500, "6782101071402635790", "506768", "4ad738f1e4cfcde6caca3c405fc54da7")
    碎块滑动             = AnimationMeta("碎块滑动", False, 0.500, "6778405418969338382", "490068", "408dbd74e95497bd0323693a477ab2a3")
    碎块滑动_II          = AnimationMeta("碎块滑动 II", False, 0.500, "6778300107113632269", "489860", "6f301b82e6b6b1ece2a98e182f6768ba")
    立方体               = AnimationMeta("立方体", False, 0.500, "6837352063496622599", "872856", "71ae450afeb88ae2620473ab790acf0e")
    立方体_II            = AnimationMeta("立方体 II", False, 0.500, "6834812485023830535", "872858", "efc0e23b864f5fcfbaa77723044c4957")
    立方体_III           = AnimationMeta("立方体 III", False, 0.500, "6834812541118452237", "872860", "dfc2e372bc454764f0b355904542e228")
    立方体_IV            = AnimationMeta("立方体 IV", False, 0.500, "6841793140949520910", "872864", "c66bf1692fe1e6bd6dd2e49eddfcd0c0")
    立方体_V             = AnimationMeta("立方体 V", False, 0.500, "6841793224663634446", "873096", "e3935dbcaf28be18a598eebb8fb79161")
    绕圈圈               = AnimationMeta("绕圈圈", False, 0.500, "6850287838441771534", "872868", "98bfd8ca3177b85246b18502832232f0")
    绕圈圈_II            = AnimationMeta("绕圈圈 II", False, 0.500, "6850287920255865357", "872872", "10eaa3db7b761a9846764a53912d7c77")
    绕圈圈_III           = AnimationMeta("绕圈圈 III", False, 0.500, "6854782718975152653", "872918", "c2e7ff02916f19a13ada361abddb8f98")
    绕圈圈_IV            = AnimationMeta("绕圈圈 IV", False, 0.500, "6854782786553778695", "872920", "1e7d503bcf05a17f175759e5c6cfbe7b")
    缩小弹动             = AnimationMeta("缩小弹动", False, 0.500, "7017689072978104869", "1392530", "8ff952ad209f2beac6dfa82b103f769e")
    缩小旋转             = AnimationMeta("缩小旋转", False, 0.500, "6760119657429996046", "447318", "8fdf7f5cf2dbd5754e523198d71dff49")
    缩小转出             = AnimationMeta("缩小转出", False, 0.500, "6805018974070247950", "638823", "1e9ab706223c64254aa89156a993a3d7")
    缩放                 = AnimationMeta("缩放", False, 0.500, "6759078592740594184", "446078", "79b143942a4f8b4b9c80b08e37994e00")
    缩放_II              = AnimationMeta("缩放 II", False, 0.500, "6779083172429697544", "493000", "e70e98fcda08b4c01969e1878d7fe9a0")
    翻转                 = AnimationMeta("翻转", False, 0.500, "6843309964732142094", "872838", "5f153d35f1c3098fef8325badc40c5e8")
    翻转_II              = AnimationMeta("翻转 II", False, 0.500, "6843310029689328135", "872840", "befc44f24a2747201d1d75e6a29753ce")
    翻转_III             = AnimationMeta("翻转 III", False, 0.500, "6843310084743762446", "872842", "b8167fc9e9deb2f638788bfc7539ed7f")
    翻转_IV              = AnimationMeta("翻转 IV", False, 0.500, "6843310129736061447", "872844", "9636ccab84e8f2c357812a38b785f6e0")
    翻转_V               = AnimationMeta("翻转 V", False, 0.500, "6843310237902967304", "872848", "4d27983b5b2899b46334d3fd999bc7d8")
    翻转_VI              = AnimationMeta("翻转 VI", False, 0.500, "6843310299991249421", "872850", "8807b174b58ee897667c08e6bee1a4f8")
    荡秋千               = AnimationMeta("荡秋千", False, 0.500, "6811007755785081357", "680643", "738c6740ca709cafb7045c4e3037cff0")
    荡秋千_II            = AnimationMeta("荡秋千 II", False, 0.500, "6811007833069326862", "680645", "bf46ce3a5478399205c1fc4bbfd7ded0")
    转入转出             = AnimationMeta("转入转出", False, 0.500, "6805012562174808590", "638793", "bb04a13adc846f9b35a4054c06e5ac59")
    转入转出_II          = AnimationMeta("转入转出 II", False, 0.500, "6818747242258633224", "701967", "e1415d8f52a5837cef9bc29e6c17cc57")
    转圈圈               = AnimationMeta("转圈圈", False, 0.500, "6829129745226011144", "741502", "234577a311bb87ef23c9b7d82174f584")
    过山车               = AnimationMeta("过山车", False, 0.500, "6870060878234915342", "911862", "64e30d2e2577f6a95313ed806807baa7")
    过山车_II            = AnimationMeta("过山车 II", False, 0.500, "6870060932928639501", "911861", "240042ee2b35aaa7f30b6e0c03e9ac4c")
    降落旋转             = AnimationMeta("降落旋转", False, 0.500, "6759075297091392007", "446076", "9c7be518c1cc63a947fdc2e311b724e9")
    魔方                 = AnimationMeta("魔方", False, 0.500, "6870060995365048840", "1362938", "b104d21e0f1b7eb044946f6bf0be1133")
    魔方_II              = AnimationMeta("魔方 II", False, 0.500, "6870061049559650829", "1362940", "53bc750124a30d746adbf44151116fde")

    # 付费特效
    分身                 = AnimationMeta("分身", True, 0.500, "6883761132645913096", "945872", "438dd3c43be7ac470cbcca81169f2cfc")
    分身_II              = AnimationMeta("分身 II", True, 0.500, "6883761226950644231", "945871", "7dbd67e473fbe25e024e0ccc91c48f29")
    动感摇晃I            = AnimationMeta("动感摇晃I", True, 0.500, "7173927429394666020", "6983415", "ceb6b5bf10aab23f1066481cefd5adfb")
    动感摇晃II           = AnimationMeta("动感摇晃II", True, 100.000, "7175103054956466744", "7129471", "4e88c30adc92ca2809d285ab67276467")
    四格滑动_II          = AnimationMeta("四格滑动 II", True, 0.500, "6883727923845534216", "945729", "1e04f7d49030244ee10eafeb832f372e")
    四格翻转_II          = AnimationMeta("四格翻转 II", True, 0.500, "6865579178599649806", "1362930", "840aa022c0a9b3b6b4876dd7084372b7")
    回忆旋转             = AnimationMeta("回忆旋转", True, 0.500, "7186961278022193722", "8300599", "fa3c068a56733a2149d5c0d03560aad8")
    坠落                 = AnimationMeta("坠落", True, 0.500, "7235902373971890747", "14020637", "0049e2c104b1ae46dbf717ea73b71234")
    弹动冲屏             = AnimationMeta("弹动冲屏", True, 0.500, "7200308690904158778", "9491799", "179cec03f1ad01245c554a599699d9ad")
    波动吸收             = AnimationMeta("波动吸收", True, 0.500, "7107468232390349349", "2786424", "ddbc0f962c69263480e6d188bf2f4b63")
    波动放大             = AnimationMeta("波动放大", True, 0.500, "7111631619768717860", "3113716", "90f87e49deba3845d73b5855dc3fa442")
    相框滑动             = AnimationMeta("相框滑动", True, 0.500, "7206139216038728248", "10166295", "517a71d78782ebc9a9718acfb865fba9")
    红酒摇晃             = AnimationMeta("红酒摇晃", True, 0.800, "6903771548436402702", "1417022", "95d79896a437524c4f94dc2902bb3b6c")
    跳跳糖               = AnimationMeta("跳跳糖", True, 0.700, "7199944821098680890", "9432783", "fc4e0cc6a2f2c775659fa9493cff9fe8")
    闪光放大             = AnimationMeta("闪光放大", True, 0.500, "7166437469909422623", "6210029", "d73e12e5f219c298b59fcca7f34fadac")
    闪光放大_II          = AnimationMeta("闪光放大 II", True, 0.500, "7166437532568130055", "6210033", "a1a110060307fb02ef62d33d5e571789")
