"""
视频片段控制器路由对比测试
验证Python版本与Kotlin版本的路由完全一致
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.controller.track_controller import track_service

from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.video_animation import VideoAnimationReqDto
from jianyingdraft_python.domain.req.transition_type_req_dto import TransitionTypeReqDto
from jianyingdraft_python.domain.req.background_filling_req_dto import BackgroundFillingReqDto
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto

from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.domain.req.transition_type import TransitionType
from jianyingdraft_python.domain.video.video_effect import VideoEffect
from jianyingdraft_python.domain.video.video_filter import VideoFilter
from jianyingdraft_python.domain.video.video_mask import VideoMask
from jianyingdraft_python.domain.timerange import Timerange


def test_route_paths_comparison():
    """测试路由路径与Kotlin版本的对比"""
    print("📋 VideoSegmentController 路由路径对比")
    print("=" * 80)
    
    # Kotlin版本的路由路径（基于VideoSegmentController.kt）
    kotlin_routes = {
        "控制器前缀": "/segment/video",
        "添加视频片段": "POST /add",
        "添加动画": "POST /add-animation", 
        "添加转场": "POST /add-transition",
        "添加背景填充": "POST /add-background-filling",
        "添加特效": "POST /add-effects",
        "添加滤镜": "POST /add-filters",
        "添加蒙版": "POST /add-mask"
    }
    
    # Python版本的路由路径（当前实现）
    python_routes = {
        "控制器前缀": "/segment/video",
        "添加视频片段": "POST /add",
        "添加动画": "POST /add-animation",
        "添加转场": "POST /add-transition", 
        "添加背景填充": "POST /add-background-filling",
        "添加特效": "POST /add-effects",
        "添加滤镜": "POST /add-filters",
        "添加蒙版": "POST /add-mask"
    }
    
    print("✅ 路由对比结果:")
    all_match = True
    
    for route_name, kotlin_path in kotlin_routes.items():
        python_path = python_routes.get(route_name, "❌ 缺失")
        match_status = "✅" if kotlin_path == python_path else "❌"
        
        if kotlin_path != python_path:
            all_match = False
            
        print(f"   {route_name}:")
        print(f"     Kotlin:  {kotlin_path}")
        print(f"     Python:  {python_path} {match_status}")
        print()
    
    if all_match:
        print("🎉 所有路由路径完全匹配！")
    else:
        print("❌ 发现路由路径不匹配！")
    
    return all_match


def test_video_segment_apis():
    """测试所有视频片段API接口"""
    print("\n🧪 测试所有视频片段API接口")
    print("=" * 80)
    
    # 1. 创建测试草稿
    print("1. 创建测试草稿...")
    draft_req = DraftCreateReqDto(
        name="路由测试草稿",
        width=1920,
        height=1080,
        fps=30,
        draftPath="/test/route_test_draft"
    )
    
    try:
        draft = draft_service.create_draft_script(draft_req)
        draft_id = draft.draftId
        print(f"   ✅ 草稿创建成功: {draft_id}")
    except Exception as e:
        print(f"   ❌ 草稿创建失败: {e}")
        return False
    
    # 2. 创建测试轨道
    print("2. 创建测试轨道...")
    track_req = TrackAddReqDto(
        draftId=draft_id,
        trackType="video",
        trackName="路由测试轨道",
        mute=False,
        relativeIndex=0,
        absoluteIndex=1
    )
    
    try:
        track = track_service.add_track(track_req)
        track_id = track.id
        print(f"   ✅ 轨道创建成功: {track_id}")
    except Exception as e:
        print(f"   ❌ 轨道创建失败: {e}")
        return False
    
    # 3. 测试 POST /add 接口
    print("3. 测试 POST /add 接口...")
    video_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/route_test_video.mp4",
        trackId=track_id,
        speed=1.0,
        volume=1.0
    )
    
    try:
        video_segment = video_service.add_video_segment(video_req)
        video_segment_id = video_segment.id
        print(f"   ✅ 视频片段添加成功: {video_segment_id}")
    except Exception as e:
        print(f"   ❌ 视频片段添加失败: {e}")
        return False
    
    # 4. 测试 POST /add-animation 接口
    print("4. 测试 POST /add-animation 接口...")
    try:
        animation_req = VideoAnimationReqDto(
            type=Resource(resource_id="fade_in", name="淡入动画"),
            duration="1s",
            draft_id=draft_id,
            video_segment_id=video_segment_id
        )
        
        result = video_service.add_animation(animation_req)
        print(f"   ✅ 视频动画添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 视频动画添加失败: {e}")
    
    # 5. 测试 POST /add-transition 接口
    print("5. 测试 POST /add-transition 接口...")
    try:
        transition_req = TransitionTypeReqDto(
            draft_id=draft_id,
            video_segment_id=video_segment_id,
            transition_type=TransitionType(
                transition_type=Resource(resource_id="fade_transition", name="淡入淡出转场"),
                duration="1s"
            )
        )
        
        result = video_service.add_transition(transition_req)
        print(f"   ✅ 转场特效添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 转场特效添加失败: {e}")
    
    # 6. 测试 POST /add-background-filling 接口
    print("6. 测试 POST /add-background-filling 接口...")
    try:
        bg_filling_req = BackgroundFillingReqDto(
            draft_id=draft_id,
            video_segment_id=video_segment_id,
            fill_type=Resource(resource_id="blur_fill", name="模糊填充")
        )
        
        result = video_service.add_background_filling(bg_filling_req)
        print(f"   ✅ 背景填充添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 背景填充添加失败: {e}")
    
    # 7. 测试 POST /add-effects 接口
    print("7. 测试 POST /add-effects 接口...")
    try:
        effect_req = VideoEffectReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            effects=[VideoEffect(
                effect_type=Resource(resource_id="blur_effect", name="模糊效果"),
                params=[50.0, 10.0]
            )]
        )
        
        result = video_service.add_effects(effect_req)
        print(f"   ✅ 视频特效添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 视频特效添加失败: {e}")
    
    # 8. 测试 POST /add-filters 接口
    print("8. 测试 POST /add-filters 接口...")
    try:
        filter_req = VideoFilterReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            filters=[VideoFilter(
                filter_type=Resource(resource_id="vintage_filter", name="复古滤镜"),
                intensity=80.0
            )]
        )
        
        result = video_service.add_filters(filter_req)
        print(f"   ✅ 视频滤镜添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 视频滤镜添加失败: {e}")
    
    # 9. 测试 POST /add-mask 接口
    print("9. 测试 POST /add-mask 接口...")
    try:
        mask_req = VideoMaskReqDto(
            draft_id=draft_id,
            segment_id=video_segment_id,
            mask=VideoMask(
                mask_type=Resource(resource_id="circle_mask", name="圆形蒙版"),
                center_x=0.0,
                center_y=0.0,
                size=0.5,
                rotation=0.0,
                feather=0.1,
                invert=False
            )
        )
        
        result = video_service.add_mask(mask_req)
        print(f"   ✅ 视频蒙版添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 视频蒙版添加失败: {e}")
    
    print("\n🎉 所有视频片段API接口测试完成！")
    return True


if __name__ == "__main__":
    print("🎯 VideoSegmentController 路由对比和功能测试")
    print("=" * 80)
    
    # 1. 测试路由路径对比
    routes_match = test_route_paths_comparison()
    
    # 2. 测试API功能
    apis_work = test_video_segment_apis()
    
    print("\n" + "=" * 60)
    if routes_match and apis_work:
        print("🎉 VideoSegmentController 与 Kotlin 版本完全一致！")
        print("✅ 所有路由路径匹配")
        print("✅ 所有API接口正常工作")
    else:
        print("❌ VideoSegmentController 与 Kotlin 版本存在差异！")
        if not routes_match:
            print("❌ 路由路径不匹配")
        if not apis_work:
            print("❌ API接口存在问题")
