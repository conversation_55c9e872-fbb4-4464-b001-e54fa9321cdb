"""
视频特效请求DTO
"""
from pydantic import BaseModel, Field
from typing import List, Optional
from ..video.video_effect import VideoEffect


class VideoEffectReqDto(BaseModel):
    """
    视频特效请求参数 - 完全匹配Kotlin版本
    """
    draft_id: str = Field(description="素材所属的 draftId", alias="draftId")
    segment_id: str = Field(description="素材所属的片段Id", alias="segmentId")
    effects: List[VideoEffect] = Field(description="视频特效参数")

    class Config:
        allow_population_by_field_name = True
