"""
测试集中式存储配置
验证所有服务使用相同的数据存储实例和路径
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.config.app_config import get_app_config
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.controller.track_controller import track_service
from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.controller.audio_segment_controller import audio_service
from jianyingdraft_python.controller.text_segment_controller import text_service
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


def test_centralized_configuration():
    """测试集中式配置"""
    print("🔧 测试集中式存储配置...")
    
    # 1. 测试配置一致性
    print("\n=== 1. 测试配置一致性 ===")
    
    config = get_app_config()
    central_store = get_data_store()
    
    print(f"📂 配置数据目录: {config.data_directory}")
    print(f"📂 存储管理器数据目录: {central_store.data_dir}")
    
    # 检查所有服务的数据存储实例
    services = {
        "draft_service": draft_service,
        "track_service": track_service,
        "video_service": video_service,
        "audio_service": audio_service,
        "text_service": text_service
    }
    
    print(f"\n📊 服务数据存储实例检查:")
    all_same = True
    for name, service in services.items():
        store_dir = service.data_store.data_dir
        is_same = store_dir == central_store.data_dir
        print(f"   {name}: {store_dir} {'✅' if is_same else '❌'}")
        if not is_same:
            all_same = False
    
    if all_same:
        print("✅ 所有服务使用相同的数据存储实例")
    else:
        print("❌ 服务使用不同的数据存储实例")
        return False
    
    # 2. 测试实际的API调用和文件创建
    print("\n=== 2. 测试实际API调用和文件创建 ===")
    
    # 检查现有草稿
    drafts = draft_service.get_all_drafts()
    print(f"📊 现有草稿数量: {len(drafts)}")
    
    if not drafts:
        print("❌ 没有现有草稿，无法测试")
        return False
    
    # 使用第一个草稿进行测试
    test_draft = drafts[0]
    draft_id = test_draft.id
    draft_name = test_draft.draft_name
    
    print(f"🎯 使用草稿进行测试:")
    print(f"   ID: {draft_id}")
    print(f"   名称: {draft_name}")
    
    # 获取草稿文件夹路径
    draft_folder = config.get_draft_folder_path(draft_name, draft_id)
    print(f"   文件夹路径: {draft_folder}")
    
    # 检查草稿文件夹是否存在
    if not draft_folder.exists():
        print(f"❌ 草稿文件夹不存在: {draft_folder}")
        return False
    
    print(f"✅ 草稿文件夹存在")
    
    # 显示当前文件夹内容
    print(f"\n📁 当前文件夹内容:")
    for item in draft_folder.iterdir():
        print(f"   📄 {item.name}")
    
    # 3. 测试轨道API
    print(f"\n=== 3. 测试轨道API ===")
    
    track_req = TrackAddReqDto(
        draftId=draft_id,
        trackType="video",
        trackName="集中配置测试轨道",
        mute=False,
        relativeIndex=0,
        absoluteIndex=1
    )
    
    print(f"📝 调用轨道API...")
    print(f"   请求数据: {track_req.model_dump()}")
    
    try:
        # 调用API前检查数据存储状态
        print(f"🔍 调用前检查:")
        print(f"   轨道服务数据存储: {track_service.data_store.data_dir}")
        print(f"   内存中轨道数量: {len(track_service.data_store._tracks)}")
        
        result = track_service.add_track(track_req)
        track_id = result.id
        
        print(f"✅ 轨道API调用成功: {track_id}")
        
        # 调用API后检查数据存储状态
        print(f"🔍 调用后检查:")
        print(f"   内存中轨道数量: {len(track_service.data_store._tracks)}")
        
        # 检查文件是否创建
        tracks_file = draft_folder / "tracks.json"
        print(f"🔍 检查轨道文件: {tracks_file}")
        
        if tracks_file.exists():
            print(f"✅ 轨道文件存在")
            
            # 读取文件内容
            import json
            with open(tracks_file, 'r', encoding='utf-8') as f:
                tracks_data = json.load(f)
            
            print(f"📄 轨道文件内容: {len(tracks_data)} 个轨道")
            
            if track_id in tracks_data:
                print(f"✅ 新轨道已保存到文件")
                track_data = tracks_data[track_id]
                print(f"   轨道名称: {track_data.get('track_name')}")
                print(f"   轨道类型: {track_data.get('track_type')}")
                
                # 继续测试其他API
                test_other_apis(draft_id, track_id, draft_folder)
                
            else:
                print(f"❌ 新轨道未保存到文件")
                print(f"   文件中的轨道ID: {list(tracks_data.keys())}")
                return False
        else:
            print(f"❌ 轨道文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 轨道API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_other_apis(draft_id: str, track_id: str, draft_folder):
    """测试其他API"""
    print(f"\n=== 4. 测试其他API ===")
    
    # 测试视频片段API
    print(f"\n📹 测试视频片段API...")
    video_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/centralized_video.mp4",
        trackId=track_id
    )
    
    try:
        video_result = video_service.add_video_segment(video_req)
        print(f"✅ 视频片段API调用成功: {video_result.id}")
        
        video_file = draft_folder / "video_segments.json"
        if video_file.exists():
            print(f"✅ 视频片段文件已创建")
        else:
            print(f"❌ 视频片段文件未创建")
            
    except Exception as e:
        print(f"❌ 视频片段API调用失败: {e}")
    
    # 测试音频片段API
    print(f"\n🎵 测试音频片段API...")
    audio_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/centralized_audio.mp3",
        trackId=track_id
    )
    
    try:
        audio_result = audio_service.add_audio_segment(audio_req)
        print(f"✅ 音频片段API调用成功: {audio_result.id}")
        
        audio_file = draft_folder / "audio_segments.json"
        if audio_file.exists():
            print(f"✅ 音频片段文件已创建")
        else:
            print(f"❌ 音频片段文件未创建")
            
    except Exception as e:
        print(f"❌ 音频片段API调用失败: {e}")
    
    # 测试文本片段API
    print(f"\n📝 测试文本片段API...")
    text_req = TextSegmentAddReqDto(
        draftId=draft_id,
        text="集中配置测试文本",
        trackId=track_id,
        targetRanger=Timerange(start="0s", duration="3s")
    )
    
    try:
        text_result = text_service.add_text_segment(text_req)
        print(f"✅ 文本片段API调用成功: {text_result.id}")
        
        text_file = draft_folder / "text_segments.json"
        if text_file.exists():
            print(f"✅ 文本片段文件已创建")
        else:
            print(f"❌ 文本片段文件未创建")
            
    except Exception as e:
        print(f"❌ 文本片段API调用失败: {e}")
    
    # 显示最终文件夹内容
    print(f"\n📁 最终文件夹内容:")
    for item in draft_folder.iterdir():
        size = item.stat().st_size if item.is_file() else 0
        print(f"   📄 {item.name} ({size} bytes)")


if __name__ == "__main__":
    success = test_centralized_configuration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 集中式存储配置测试成功！")
    else:
        print("❌ 集中式存储配置测试失败！")
