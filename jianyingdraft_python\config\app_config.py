"""
应用程序集中配置
统一管理所有路径和配置项
"""
import os
from pathlib import Path
from typing import Optional


class AppConfig:
    """应用程序配置类"""
    
    # 单例实例
    _instance: Optional['AppConfig'] = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_paths()
            AppConfig._initialized = True
    
    def _setup_paths(self):
        """设置所有路径"""
        # 获取项目根目录（jianyingdraft_python文件夹）
        self.project_root = Path(__file__).parent.parent
        
        # 数据存储目录
        self.data_dir = self.project_root / "data"
        
        # 确保数据目录存在
        self.data_dir.mkdir(exist_ok=True)
        
        # 日志目录
        self.log_dir = self.project_root / "logs"
        self.log_dir.mkdir(exist_ok=True)
        
        # 临时文件目录
        self.temp_dir = self.project_root / "temp"
        self.temp_dir.mkdir(exist_ok=True)
        
        print(f"📂 AppConfig 初始化完成:")
        print(f"   项目根目录: {self.project_root}")
        print(f"   数据目录: {self.data_dir}")
        print(f"   日志目录: {self.log_dir}")
        print(f"   临时目录: {self.temp_dir}")
    
    @property
    def data_directory(self) -> str:
        """获取数据目录路径（字符串格式）"""
        return str(self.data_dir)
    
    @property
    def data_directory_path(self) -> Path:
        """获取数据目录路径（Path对象）"""
        return self.data_dir
    
    def get_draft_folder_path(self, draft_name: Optional[str], draft_id: str) -> Path:
        """获取草稿文件夹路径"""
        import re
        
        if draft_name and draft_name.strip():
            # 清理文件夹名称，移除不合法字符
            folder_name = re.sub(r'[<>:"/\\|?*]', '_', draft_name.strip())
        else:
            folder_name = draft_id
        
        return self.data_dir / folder_name
    
    def get_draft_file_path(self, draft_name: Optional[str], draft_id: str, filename: str) -> Path:
        """获取草稿文件路径"""
        draft_folder = self.get_draft_folder_path(draft_name, draft_id)
        return draft_folder / filename
    
    def ensure_draft_folder_exists(self, draft_name: Optional[str], draft_id: str) -> Path:
        """确保草稿文件夹存在"""
        draft_folder = self.get_draft_folder_path(draft_name, draft_id)
        draft_folder.mkdir(exist_ok=True)
        return draft_folder


# 全局配置实例
app_config = AppConfig()


def get_app_config() -> AppConfig:
    """获取应用配置实例"""
    return app_config


def get_data_directory() -> str:
    """获取数据目录路径"""
    return app_config.data_directory


def get_data_directory_path() -> Path:
    """获取数据目录路径（Path对象）"""
    return app_config.data_directory_path
