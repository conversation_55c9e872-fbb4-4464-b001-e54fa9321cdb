from typing import Dict, Any
from jianyingdraft_python.entity.track_entity import TrackEntity
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.storage.data_store import DataStore
import uuid


class TrackService:
    """轨道服务类"""

    def __init__(self):
        self.data_store = DataStore()

    def add_track(self, req_dto: TrackAddReqDto) -> TrackEntity:
        """添加轨道"""
        track_id = str(uuid.uuid4()).upper()

        track = TrackEntity(
            id=track_id,
            draft_id=req_dto.draftId,
            track_type=req_dto.trackType,
            track_name=req_dto.trackName,
            mute=req_dto.mute,
            relative_index=req_dto.relativeIndex,
            absolute_index=req_dto.absoluteIndex
        )

        # 保存轨道
        self.data_store.save_track(track)
        return track

    def get_tracks_by_draft(self, draft_id: str) -> list[TrackEntity]:
        """获取草稿的轨道"""
        return self.data_store.get_tracks_by_draft(draft_id)

    def delete_track(self, track_id: str):
        """删除轨道"""
        # 检查轨道是否存在
        track = None
        for t in self.data_store._tracks.values():
            if t.id == track_id:
                track = t
                break

        if not track:
            raise SysException.not_found("轨道不存在")

        # 删除轨道
        self.data_store.delete_track(track_id)