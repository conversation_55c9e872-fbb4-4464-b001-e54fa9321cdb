"""
测试新的存储结构 - 每个草稿使用独立文件夹
"""
import sys
import os
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.service.video_segment_service import VideoSegmentService
from jianyingdraft_python.service.audio_segment_service import AudioSegmentService
from jianyingdraft_python.service.text_segment_service import TextSegmentService
from jianyingdraft_python.service.track_service import TrackService
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.timerange import Timerange


def test_new_storage_structure():
    """测试新的存储结构"""
    print("🚀 测试新的存储结构...")
    
    # 创建临时数据目录
    temp_data_dir = tempfile.mkdtemp(prefix="test_new_storage_")
    print(f"📂 临时数据目录: {temp_data_dir}")
    
    try:
        # 使用临时目录创建服务
        from jianyingdraft_python.storage.data_store import DataStore
        
        # 创建使用临时目录的数据存储
        data_store = DataStore(temp_data_dir)
        
        # 创建服务实例（它们会自动使用新的数据存储）
        draft_service = DraftService()
        draft_service.data_store = data_store  # 替换为临时数据存储
        
        video_service = VideoSegmentService()
        video_service.data_store = data_store
        
        audio_service = AudioSegmentService()
        audio_service.data_store = data_store
        
        text_service = TextSegmentService()
        text_service.data_store = data_store
        
        track_service = TrackService()
        track_service.data_store = data_store
        
        # 1. 创建草稿
        print("\n=== 1. 创建草稿 ===")
        draft_req = DraftCreateReqDto(
            width=1920,
            height=1080,
            fps=30,
            name="新存储测试草稿",
            draftPath=os.path.join(temp_data_dir, "draft_output")
        )
        
        draft_result = draft_service.create_draft_script(draft_req)
        draft_id = draft_result.draftId
        print(f"✅ 创建草稿成功: {draft_id}")
        
        # 检查草稿文件夹是否创建
        expected_folder = os.path.join(temp_data_dir, "新存储测试草稿")
        if os.path.exists(expected_folder):
            print(f"✅ 草稿文件夹创建成功: {expected_folder}")
            
            # 检查draft.json文件
            draft_file = os.path.join(expected_folder, "draft.json")
            if os.path.exists(draft_file):
                print(f"✅ 草稿文件创建成功: {draft_file}")
            else:
                print(f"❌ 草稿文件未创建: {draft_file}")
        else:
            print(f"❌ 草稿文件夹未创建: {expected_folder}")
        
        # 2. 添加轨道
        print("\n=== 2. 添加轨道 ===")
        track_req = TrackAddReqDto(
            draftId=draft_id,
            trackType="video",
            trackName="主视频轨道",
            mute=False,
            relativeIndex=0
        )
        
        track_result = track_service.add_track(track_req)
        track_id = track_result.id
        print(f"✅ 添加轨道成功: {track_id}")
        
        # 检查tracks.json文件
        tracks_file = os.path.join(expected_folder, "tracks.json")
        if os.path.exists(tracks_file):
            print(f"✅ 轨道文件创建成功: {tracks_file}")
        else:
            print(f"❌ 轨道文件未创建: {tracks_file}")
        
        # 3. 添加视频片段
        print("\n=== 3. 添加视频片段 ===")
        video_req = MediaSegmentAddReqDto(
            draftId=draft_id,
            targetTimerange=Timerange(start="0s", duration="5s"),
            resourcePath="/test/video.mp4",
            trackId=track_id
        )
        
        video_result = video_service.add_video_segment(video_req)
        print(f"✅ 添加视频片段成功: {video_result.id}")
        
        # 检查video_segments.json文件
        video_file = os.path.join(expected_folder, "video_segments.json")
        if os.path.exists(video_file):
            print(f"✅ 视频片段文件创建成功: {video_file}")
        else:
            print(f"❌ 视频片段文件未创建: {video_file}")
        
        # 4. 添加音频片段
        print("\n=== 4. 添加音频片段 ===")
        audio_req = MediaSegmentAddReqDto(
            draftId=draft_id,
            targetTimerange=Timerange(start="0s", duration="5s"),
            resourcePath="/test/audio.mp3",
            trackId=track_id
        )
        
        audio_result = audio_service.add_audio_segment(audio_req)
        print(f"✅ 添加音频片段成功: {audio_result.id}")
        
        # 检查audio_segments.json文件
        audio_file = os.path.join(expected_folder, "audio_segments.json")
        if os.path.exists(audio_file):
            print(f"✅ 音频片段文件创建成功: {audio_file}")
        else:
            print(f"❌ 音频片段文件未创建: {audio_file}")
        
        # 5. 添加文本片段
        print("\n=== 5. 添加文本片段 ===")
        text_req = TextSegmentAddReqDto(
            draftId=draft_id,
            text="测试文本",
            trackId=track_id,
            targetRanger=Timerange(start="0s", duration="3s")
        )
        
        text_result = text_service.add_text_segment(text_req)
        print(f"✅ 添加文本片段成功: {text_result.id}")
        
        # 检查text_segments.json文件
        text_file = os.path.join(expected_folder, "text_segments.json")
        if os.path.exists(text_file):
            print(f"✅ 文本片段文件创建成功: {text_file}")
        else:
            print(f"❌ 文本片段文件未创建: {text_file}")
        
        # 6. 显示最终的文件夹结构
        print("\n=== 6. 最终文件夹结构 ===")
        show_directory_structure(temp_data_dir)
        
        # 7. 测试数据读取
        print("\n=== 7. 测试数据读取 ===")
        
        # 重新创建数据存储实例来测试加载
        new_data_store = DataStore(temp_data_dir)
        
        drafts = new_data_store.get_all_drafts()
        print(f"✅ 加载草稿数量: {len(drafts)}")
        
        if drafts:
            draft = drafts[0]
            print(f"  草稿名称: {draft.draft_name}")
            print(f"  草稿ID: {draft.id}")
            
            # 测试获取各种片段
            video_segments = new_data_store.get_video_segments_by_draft(draft.id)
            audio_segments = new_data_store.get_audio_segments_by_draft(draft.id)
            text_segments = new_data_store.get_text_segments_by_draft(draft.id)
            tracks = new_data_store.get_tracks_by_draft(draft.id)
            
            print(f"  视频片段数量: {len(video_segments)}")
            print(f"  音频片段数量: {len(audio_segments)}")
            print(f"  文本片段数量: {len(text_segments)}")
            print(f"  轨道数量: {len(tracks)}")
        
        print("\n✅ 新存储结构测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_data_dir)
            print(f"🧹 清理临时目录: {temp_data_dir}")
        except Exception as e:
            print(f"⚠️ 清理临时目录失败: {e}")


def show_directory_structure(root_dir, prefix="", max_depth=3, current_depth=0):
    """显示目录结构"""
    if current_depth >= max_depth:
        return
        
    try:
        items = sorted(os.listdir(root_dir))
        for i, item in enumerate(items):
            item_path = os.path.join(root_dir, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(item_path):
                next_prefix = prefix + ("    " if is_last else "│   ")
                show_directory_structure(item_path, next_prefix, max_depth, current_depth + 1)
    except PermissionError:
        print(f"{prefix}[Permission Denied]")


if __name__ == "__main__":
    test_new_storage_structure()
