import pytest
import json
import os
import requests
from typing import Dict, Any, List


class TestAPIExport:
    """测试API导出功能"""

    def setup_method(self):
        """测试前的设置"""
        # API服务地址
        self.api_base_url = "http://localhost:8000/api/v1"
        
        # 测试数据路径
        self.test_data_path = r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目"
        
        # 输出文件夹
        self.output_folder = r"D:\PythonProject\my-jianying\jianyingdraft_python\data\综合API功能测试项目\output"
        os.makedirs(self.output_folder, exist_ok=True)

    def load_test_data(self):
        """加载测试数据"""
        # 加载草稿数据
        with open(os.path.join(self.test_data_path, "draft.json"), 'r', encoding='utf-8') as f:
            draft_data = json.load(f)
        
        # 加载轨道数据
        with open(os.path.join(self.test_data_path, "tracks.json"), 'r', encoding='utf-8') as f:
            tracks_data = json.load(f)
        
        # 加载音频片段数据
        with open(os.path.join(self.test_data_path, "audio_segments.json"), 'r', encoding='utf-8') as f:
            audio_segments_data = json.load(f)
        
        # 加载视频片段数据
        with open(os.path.join(self.test_data_path, "video_segments.json"), 'r', encoding='utf-8') as f:
            video_segments_data = json.load(f)
        
        return draft_data, tracks_data, audio_segments_data, video_segments_data

    def organize_tracks_with_segments(self, tracks_data, audio_segments_data, video_segments_data):
        """组织轨道数据，模拟Kotlin项目中的mediaTracks方法"""
        audio_tracks = []
        video_tracks = []
        text_tracks = []
        
        for track_id, track_data in tracks_data.items():
            track_info = {
                "trackId": track_id,
                "trackName": track_data["track_name"],
                "trackType": track_data["track_type"],
                "mute": track_data["mute"],
                "relativeIndex": track_data["relative_index"],
                "absoluteIndex": track_data["absolute_index"]
            }
            
            if track_data["track_type"] == "audio":
                # 查找属于此轨道的音频片段
                segments = []
                for segment_id, segment_data in audio_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        segments.append(segment_data)
                
                track_info["segments"] = segments
                audio_tracks.append(track_info)
                
            elif track_data["track_type"] == "video":
                # 查找属于此轨道的视频片段
                segments = []
                for segment_id, segment_data in video_segments_data.items():
                    if segment_data["track_id"] == track_id:
                        segments.append(segment_data)
                
                track_info["segments"] = segments
                video_tracks.append(track_info)
                
            elif track_data["track_type"] == "text":
                # 文本轨道（暂时为空）
                track_info["segments"] = []
                text_tracks.append(track_info)
        
        return audio_tracks, video_tracks, text_tracks

    def create_draft_result_dto(self, draft_data, audio_tracks, video_tracks, text_tracks):
        """创建DraftResultResDto格式的请求数据"""
        return {
            "draftId": draft_data["id"],
            "draftName": draft_data["draft_name"],
            "audioTracks": audio_tracks,
            "videoTracks": video_tracks,
            "textTracks": text_tracks
        }

    def save_response_to_files(self, response_data, draft_id):
        """保存API响应到文件"""
        # 保存完整响应
        response_file = os.path.join(self.output_folder, f"api_response_{draft_id}.json")
        with open(response_file, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
        
        # 如果响应包含data字段，单独保存
        if "data" in response_data and response_data["data"]:
            draft_content_file = os.path.join(self.output_folder, f"draft_content_{draft_id}.json")
            with open(draft_content_file, 'w', encoding='utf-8') as f:
                json.dump(response_data["data"], f, ensure_ascii=False, indent=2)
        
        print(f"✅ API响应已保存到: {self.output_folder}")
        print(f"   - 完整响应: {response_file}")
        if "data" in response_data and response_data["data"]:
            print(f"   - 草稿内容: {draft_content_file}")
        
        return response_file

    def test_api_export_script(self):
        """测试API导出脚本接口"""
        # 1. 加载测试数据
        draft_data, tracks_data, audio_segments_data, video_segments_data = self.load_test_data()
        
        # 2. 组织轨道数据
        audio_tracks, video_tracks, text_tracks = self.organize_tracks_with_segments(
            tracks_data, audio_segments_data, video_segments_data
        )
        
        # 3. 创建请求数据
        request_data = self.create_draft_result_dto(
            draft_data, audio_tracks, video_tracks, text_tracks
        )
        
        # 4. 保存请求数据（用于调试）
        request_file = os.path.join(self.output_folder, f"request_data_{draft_data['id']}.json")
        with open(request_file, 'w', encoding='utf-8') as f:
            json.dump(request_data, f, ensure_ascii=False, indent=2)
        print(f"📤 请求数据已保存到: {request_file}")
        
        # 5. 调用API接口
        api_url = f"{self.api_base_url}/drafts/export_script"
        
        print(f"🚀 开始调用API: {api_url}")
        print(f"📊 请求数据概览:")
        print(f"   - 草稿ID: {request_data['draftId']}")
        print(f"   - 草稿名称: {request_data['draftName']}")
        print(f"   - 音频轨道数: {len(request_data['audioTracks'])}")
        print(f"   - 视频轨道数: {len(request_data['videoTracks'])}")
        print(f"   - 文本轨道数: {len(request_data['textTracks'])}")
        
        try:
            response = requests.post(
                api_url,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"📡 API响应状态码: {response.status_code}")
            
            # 6. 处理响应
            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ API调用成功")
                print(f"📋 响应概览:")
                print(f"   - 状态码: {response_data.get('code', 'N/A')}")
                print(f"   - 消息: {response_data.get('message', 'N/A')}")
                print(f"   - 数据存在: {'是' if response_data.get('data') else '否'}")
                
                # 保存响应到文件
                response_file = self.save_response_to_files(response_data, draft_data["id"])
                
                # 验证响应数据
                assert response_data.get("code") == 200, f"API返回错误码: {response_data.get('code')}"
                assert response_data.get("data") is not None, "API返回数据为空"
                
                # 验证返回的数据结构
                draft_content = response_data["data"]
                assert isinstance(draft_content, dict), "返回的草稿内容不是字典格式"
                
                # 检查关键字段
                expected_fields = ["materials", "tracks", "canvas_config"]
                for field in expected_fields:
                    if field in draft_content:
                        print(f"   ✅ 包含字段: {field}")
                    else:
                        print(f"   ⚠️  缺少字段: {field}")
                
                print(f"🎉 测试通过！草稿导出成功")
                
            else:
                print(f"❌ API调用失败，状态码: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                
                # 保存错误响应
                error_file = os.path.join(self.output_folder, f"error_response_{draft_data['id']}.txt")
                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write(f"Status Code: {response.status_code}\n")
                    f.write(f"Response: {response.text}\n")
                
                pytest.fail(f"API调用失败，状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            pytest.fail(f"网络请求异常: {e}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            pytest.fail(f"测试异常: {e}")

    def test_api_connectivity(self):
        """测试API连通性"""
        try:
            # 测试基础连通性
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            print(f"🔗 API连通性测试: {response.status_code}")
            
            if response.status_code != 200:
                print(f"⚠️  API服务可能未正常启动，状态码: {response.status_code}")
            else:
                print(f"✅ API服务正常运行")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API连通性测试失败: {e}")
            print(f"💡 请确保API服务已启动: python api/run.py")
            pytest.fail(f"API服务连接失败: {e}")

    def test_full_export_workflow(self):
        """测试完整的导出工作流程"""
        print("🚀 开始完整导出工作流程测试")
        
        # 1. 测试API连通性
        print("\n📡 步骤1: 测试API连通性")
        self.test_api_connectivity()
        
        # 2. 测试导出脚本
        print("\n📊 步骤2: 测试导出脚本")
        self.test_api_export_script()
        
        print("\n🎉 完整导出工作流程测试完成！")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
