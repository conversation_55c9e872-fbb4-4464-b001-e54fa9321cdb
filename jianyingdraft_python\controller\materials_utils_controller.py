"""
素材工具控制器
提供素材工具相关的接口
"""
from fastapi import APIRouter, HTTPException, Query
from jianyingdraft_python.service.materials_utils_service import MaterialsUtilsService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/materials/utils", tags=["素材工具接口"])

# 使用集中式存储管理器
print("🔧 MaterialsUtilsController: 初始化服务...")
materials_utils_service = MaterialsUtilsService()
materials_utils_service.data_store = get_data_store()
print("✅ MaterialsUtilsController: 服务初始化完成")


@router.get("/media-info", response_model=DataResponse[MediaInfo])
async def media_info(file_path: str = Query(..., description="文件路径")):
    """获取素材信息"""
    try:
        result = materials_utils_service.media_info(file_path)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
