from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.text_segment_service import TextSegmentService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.text_segment_add_req_dto import TextSegmentAddReqDto
from jianyingdraft_python.domain.req.text_animation_and_effect import TextAnimationAndEffectReqDto
from jianyingdraft_python.entity.text_segment_entity import TextSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

# 使用Kotlin兼容的路径
router = APIRouter(prefix="/segment/text", tags=["文本片段管理"])

# 使用集中式存储管理器
print("🔧 TextController: 初始化服务...")
text_service = TextSegmentService()
text_service.data_store = get_data_store()
print("✅ TextController: 服务初始化完成")


@router.post("/add", response_model=DataResponse[TextSegmentEntity])
async def add_text_segment(req_dto: TextSegmentAddReqDto):
    """添加文本片段"""
    try:
        result = text_service.add_text_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.post("/add-animation-effect", response_model=DataResponse[str])
async def add_or_update_text_animation_and_effect_to_segment(req_dto: TextAnimationAndEffectReqDto):
    """给字幕片段添加或更新动画和特效"""
    try:
        result = text_service.add_or_update_text_animation_and_effect_to_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))