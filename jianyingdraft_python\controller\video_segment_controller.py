from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.video_segment_service import VideoSegmentService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.video_animation import VideoAnimationReqDto
from jianyingdraft_python.domain.req.transition_type_req_dto import TransitionTypeReqDto
from jianyingdraft_python.domain.req.background_filling_req_dto import BackgroundFillingReqDto
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto
from jianyingdraft_python.entity.video_segment_entity import VideoSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException

# 使用Kotlin兼容的路径
router = APIRouter(prefix="/segment/video", tags=["视频片段管理"])

# 使用集中式存储管理器
print("🔧 VideoController: 初始化服务...")
video_service = VideoSegmentService()
video_service.data_store = get_data_store()
print("✅ VideoController: 服务初始化完成")


@router.post("/add", response_model=DataResponse[VideoSegmentEntity])
async def add_video_segment(req_dto: MediaSegmentAddReqDto):
    """添加视频片段"""
    try:
        result = video_service.add_video_segment(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.post("/add-animation", response_model=DataResponse[str])
async def add_animation(req_dto: VideoAnimationReqDto):
    """给视频片段添加动画"""
    try:
        result = video_service.add_animation(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-transition", response_model=DataResponse[str])
async def add_transition(req_dto: TransitionTypeReqDto):
    """给视频片段添加转场特效"""
    try:
        result = video_service.add_transition(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-background-filling", response_model=DataResponse[str])
async def add_background_filling(req_dto: BackgroundFillingReqDto):
    """给视频片段添加背景填充"""
    try:
        result = video_service.add_background_filling(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-effects", response_model=DataResponse[str])
async def add_effects(req_dto: VideoEffectReqDto):
    """给视频片段添加特效"""
    try:
        result = video_service.add_effects(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-filters", response_model=DataResponse[str])
async def add_filters(req_dto: VideoFilterReqDto):
    """给视频片段添加滤镜"""
    try:
        result = video_service.add_filters(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add-mask", response_model=DataResponse[str])
async def add_mask(req_dto: VideoMaskReqDto):
    """给视频片段添加蒙版"""
    try:
        result = video_service.add_mask(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=400, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))