from typing import Dict, Any
import uuid
import os
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.resource import AudioFadeEffectReqDto, AudioKeyframeReqDto
from jianyingdraft_python.domain.req.audio_effect_req_dto import AudioEffectReqDto
from jianyingdraft_python.domain.meterial.media_info import MediaInfo
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.utils.draft_utils import DraftUtils
from jianyingdraft_python.utils.time_utils import TimeUtils
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.service.resource_validation_service import get_resource_validation_service


class AudioSegmentService:
    """音频片段服务类"""

    def __init__(self):
        self.data_store = DataStore()

    def add_audio_segment(self, req_dto: MediaSegmentAddReqDto) -> AudioSegmentEntity:
        """添加音频片段"""
        # 检查时间重叠
        segments_data = self.data_store.get_segments_data()
        DraftUtils.check_segment_overlap_in_track(
            draft_id=req_dto.draftId,
            track_id=req_dto.trackId,
            new_segment_timerange=req_dto.targetTimerange,
            segment_type="audio",
            exclude_segment_id=None,
            segments_data=segments_data
        )

        # 计算实际时间范围
        # 使用源时间范围的持续时间，如果没有则使用目标时间范围的持续时间
        calculated_duration = (
            req_dto.sourceTimerange.duration if req_dto.sourceTimerange
            else req_dto.targetTimerange.duration
        )

        target_timerange = DraftUtils.build_complete_target_timerange(
            original_timerange=req_dto.targetTimerange,
            after_segment_id=req_dto.afterSegmentId,
            calculated_duration=calculated_duration,
            segments_data=segments_data
        )
        
        real_target_timerange = TimeUtils.calculate_timeline(target_timerange)

        segment_id = str(uuid.uuid4()).upper()

        # 创建默认的 MediaInfo（从资源路径推断）
        file_name = os.path.basename(req_dto.resourcePath)
        media_info = MediaInfo(
            file_name=file_name,
            absolute_path=req_dto.resourcePath,
            file_size=0,  # 默认值，实际应该读取文件
            mime_type="audio/mp3",  # 默认值
            type="mp3",  # 默认值
            duration_microseconds=10000000,  # 10秒，默认值
            duration_seconds="10s"  # 默认值
        )

        segment = AudioSegmentEntity(
            id=segment_id,
            draft_id=req_dto.draftId,
            target_timerange=target_timerange,
            real_target_timerange=real_target_timerange,
            source_timerange=req_dto.sourceTimerange,
            resource_path=req_dto.resourcePath,
            track_id=req_dto.trackId,
            media_info=media_info,
            volume=req_dto.volume,
            speed=req_dto.speed
        )

        # 保存片段
        self.data_store.save_audio_segment(segment)
        return segment

    def get_audio_segments_by_draft(self, draft_id: str) -> list[AudioSegmentEntity]:
        """获取草稿的音频片段"""
        return self.data_store.get_audio_segments_by_draft(draft_id)

    def add_audio_fade_effect(self, req_dto: AudioFadeEffectReqDto) -> str:
        """给音频片段添加或更新淡入淡出特效"""
        # 获取音频片段
        segment = self._get_audio_segment(req_dto.draft_id, req_dto.audio_segment_id)

        # 设置淡入淡出特效
        segment.fade_effect = req_dto.audio_fade

        # 保存更新后的片段
        self.data_store.save_audio_segment(segment)

        return req_dto.audio_segment_id

    def add_audio_keyframe(self, req_dto: AudioKeyframeReqDto) -> str:
        """给音频片段批量添加关键帧"""
        # 获取音频片段
        segment = self._get_audio_segment(req_dto.draft_id, req_dto.audio_segment_id)

        # 添加关键帧（合并新关键帧到现有关键帧列表）
        if segment.audio_keyframes is None:
            segment.audio_keyframes = []

        # 根据timeOffset去重，同一时间点只能有一个关键帧
        existing_time_offsets = {kf.time_offset for kf in segment.audio_keyframes}

        for new_keyframe in req_dto.keyframes:
            # 将KeyframeData转换为AudioKeyframe
            from jianyingdraft_python.entity.audio_segment_entity import AudioKeyframe
            audio_keyframe = AudioKeyframe(
                time_offset=new_keyframe.time_offset,
                volume=new_keyframe.volume
            )

            if new_keyframe.time_offset not in existing_time_offsets:
                segment.audio_keyframes.append(audio_keyframe)
            else:
                # 替换现有的同时间点关键帧
                segment.audio_keyframes = [
                    kf for kf in segment.audio_keyframes
                    if kf.time_offset != new_keyframe.time_offset
                ]
                segment.audio_keyframes.append(audio_keyframe)

        # 保存更新后的片段
        self.data_store.save_audio_segment(segment)

        return req_dto.audio_segment_id

    def add_audio_effects(self, req_dto: AudioEffectReqDto) -> str:
        """给音频片段添加特效"""
        # 资源验证
        resource_validator = get_resource_validation_service()
        effect_resources = [effect.effect_type for effect in req_dto.audio_effects]
        validation_result = resource_validator.validate_resources(effect_resources, "audio_effects")

        if validation_result["warnings"]:
            print(f"⚠️ 音频特效资源验证警告: {'; '.join(validation_result['warnings'])}")

        # 获取音频片段
        segment = self._get_audio_segment(req_dto.draft_id, req_dto.segment_id)

        # 添加特效（合并新特效到现有特效列表）
        if segment.audio_effects is None:
            segment.audio_effects = []

        # 根据resourceId去重，同一类型特效只能有一个
        existing_effect_types = {effect.effect_type.resource_id for effect in segment.audio_effects}

        for new_effect in req_dto.audio_effects:
            if new_effect.effect_type.resource_id not in existing_effect_types:
                segment.audio_effects.append(new_effect)
            else:
                # 替换现有的同类型特效
                segment.audio_effects = [
                    effect for effect in segment.audio_effects
                    if effect.effect_type.resource_id != new_effect.effect_type.resource_id
                ]
                segment.audio_effects.append(new_effect)

        # 保存更新后的片段
        self.data_store.save_audio_segment(segment)

        return req_dto.segment_id

    def _get_audio_segment(self, draft_id: str, segment_id: str) -> AudioSegmentEntity:
        """获取音频片段"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")

        # 获取音频片段
        segment = None
        for seg_id, seg in self.data_store._audio_segments.items():
            if seg.id == segment_id and seg.draft_id == draft_id:
                segment = seg
                break

        if not segment:
            raise SysException.not_found("音频片段不存在")

        return segment