"""
资源验证控制器
提供资源验证和查询相关的接口
"""
from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any
from jianyingdraft_python.service.resource_validation_service import get_resource_validation_service
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/api/resources", tags=["资源验证接口"])

# 获取资源验证服务
resource_validation_service = get_resource_validation_service()


@router.get("/categories", response_model=DataResponse[List[str]])
async def get_resource_categories():
    """获取所有资源类别"""
    try:
        categories = resource_validation_service.get_all_categories()
        return DataResponse.success(categories)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/available/{category}", response_model=DataResponse[List[str]])
async def get_available_resources(category: str):
    """获取指定类别的可用资源列表"""
    try:
        resources = resource_validation_service.get_available_resources(category)
        return DataResponse.success(resources)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate", response_model=DataResponse[Dict[str, Any]])
async def validate_resource(
    resource_id: str = Query(..., description="资源ID"),
    resource_name: str = Query(..., description="资源名称"),
    category: str = Query(..., description="资源类别")
):
    """验证单个资源"""
    try:
        resource = Resource(resource_id=resource_id, resource_name=resource_name)
        result = resource_validation_service.validate_resource(resource, category)
        return DataResponse.success(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refresh")
async def refresh_resources():
    """刷新资源缓存"""
    try:
        resource_validation_service.refresh_resources()
        return DataResponse.success("资源缓存已刷新")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats", response_model=DataResponse[Dict[str, Any]])
async def get_resource_stats():
    """获取资源统计信息"""
    try:
        categories = resource_validation_service.get_all_categories()
        stats = {}
        
        for category in categories:
            resources = resource_validation_service.get_available_resources(category)
            stats[category] = {
                "count": len(resources),
                "resources": resources[:10]  # 只显示前10个作为示例
            }
        
        return DataResponse.success(stats)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
