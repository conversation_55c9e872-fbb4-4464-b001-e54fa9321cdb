import io
from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.draft_service import DraftService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/draft", tags=["草稿管理"])

# 使用集中式存储管理器
print("🔧 DraftController: 初始化服务...")
draft_service = DraftService()
draft_service.data_store = get_data_store()
print("✅ DraftController: 服务初始化完成")


@router.post("/create", response_model=DataResponse[DraftCreateRepDto])
async def create_draft(req_dto: DraftCreateReqDto):
    """创建草稿"""
    try:
        result = draft_service.create_draft_script(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{draftId}/export")
async def export_draft_as_zip(draftId: str):
    """导出草稿为zip压缩包"""
    try:
        zip_data = draft_service.export_draft_as_zip(draftId)
        from fastapi.responses import StreamingResponse
        
        return StreamingResponse(
            io.BytesIO(zip_data),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=draft_{draftId}.zip"}
        )
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


