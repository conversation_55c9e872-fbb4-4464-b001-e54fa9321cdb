import uuid
import os
from datetime import datetime
from jianyingdraft_python.entity.draft_entity import DraftEntity
from jianyingdraft_python.domain.rep.draft_create_rep_dto import DraftCreateRepDto
from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.exception.sys_exception import SysException
from jianyingdraft_python.storage.data_store import DataStore
import json
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from api.transform_to_obj import convert_dict_to_object
from pyJianYingDraft import (
    AudioSegment, VideoSegment, ClipSettings, TextSegment, TextStyle, TextBorder,
    TextBackground, Timerange, AudioMaterial, VideoMaterial, CropSettings
)
from pyJianYingDraft.metadata.effect_meta import EffectEnum
import pyJianYingDraft as draft
DEFAULT_FONT = {"resourceName": "文轩体", "resourceId": "7290445778273702455"}
DEFAULT_ANIMATION_DURATION = 500000
DEFAULT_VOLUME = 0.6
DEFAULT_SPEED = 1.0
DEFAULT_BLUR = 0.0625
DEFAULT_BACKGROUND_COLOR = "#00000000"

# 配置日志记录器
logger = logging.getLogger(__name__)


class DraftConfig:
    """
    草稿配置类 - 管理草稿的配置信息
    """

    def __init__(self, width: int = 1920, height: int = 1080, fps: int = 30) -> None:
        self.default_width = width
        self.default_height = height
        self.default_fps = fps


class TrackProcessor(ABC):
    """
    轨道处理器抽象基类
    """

    def __init__(self, config: DraftConfig) -> None:
        self.config = config

    @abstractmethod
    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        """处理轨道数据的抽象方法"""
        pass

    def add_track_to_script(self, script: draft.ScriptFile, track_data: Dict[str, Any],
                            track_type: draft.TrackType) -> None:
        """向脚本添加轨道"""
        try:
            track_name = track_data.get("track_name")
            script.add_track(
                track_type=track_type,
                track_name=track_name,
                relative_index=track_data.get("relative_index", 0),
                absolute_index=track_data.get("absolute_index"),
                mute=track_data.get("mute", False)
            )
            logger.info(f"成功添加{track_type.name}轨道: {track_name}")
        except Exception as e:
            logger.error(f"添加轨道失败: {e}")
            raise

    def _get_effect_from_data(self, effect_data: Dict[str, Any]) -> Optional[Any]:
        """从效果数据中获取效果对象"""
        resource_name = effect_data.get("resource_name")
        return EffectEnum.from_name(resource_name)


class AudioTrackProcessor(TrackProcessor):
    """
    音频轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('track_name')
        logger.info(f"开始处理音频轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.audio)

        audio_segments = track_data.get('audio_segments')
        if audio_segments:
            self._process_audio_segments(script, track_name, audio_segments)

    def _process_audio_segments(self, script: draft.ScriptFile, track_name: str,
                                audio_segments: List[Dict[str, Any]]) -> None:
        """处理音频片段列表"""
        for segment_data in audio_segments:
            try:
                audio_segment = self._create_audio_segment(segment_data)
                self._add_all_effects(audio_segment, segment_data)
                script.add_segment(audio_segment, track_name=track_name)
                logger.info(f"成功添加音频片段到轨道: {track_name}")
            except Exception as e:
                logger.error(f"处理音频片段失败: {e}")
                raise

    def _add_all_effects(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加所有音频效果"""
        self._apply_audio_fade(audio_segment, segment_data)
        self._add_keyframes(audio_segment, segment_data)
        self._add_effects(audio_segment, segment_data)

    def _add_effects(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加音频效果"""
        audio_effects = segment_data.get('audio_effects', [])
        for effect_data in audio_effects:
            effect_type = effect_data.get('effect_type')
            if effect_type:
                effect_meta = self._get_effect_from_data(effect_type)
                if effect_meta:
                    audio_segment.add_effect(effect_meta, effect_data.get('params'))
                    logger.info(f"添加音频片段效果: {effect_meta.value.name}")

    def _add_keyframes(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """添加音频关键帧"""
        keyframes = segment_data.get('audio_keyframes')
        if keyframes:
            for keyframe in keyframes:
                time_offset = keyframe.get('time_offset')
                volume = keyframe.get('volume', 1.0)
                audio_segment.add_keyframe(time_offset, volume)
                logger.info(f"添加音频片段关键帧: {keyframe}")

    def _apply_audio_fade(self, audio_segment: AudioSegment, segment_data: Dict[str, Any]) -> None:
        """应用音频淡入淡出效果"""
        fade_effect = segment_data.get('fade_effect')
        if fade_effect:
            in_duration = fade_effect.get('fade_in_duration')
            out_duration = fade_effect.get('fade_out_duration')
            audio_segment.add_fade(in_duration, out_duration)
            logger.info("应用音频淡入淡出效果")

    def _create_audio_segment(self, segment_data: Dict[str, Any]) -> AudioSegment:
        """创建音频片段"""
        resource_path = segment_data.get('resource_path')
        media_info = segment_data.get('media_info', {})

        audio_material = AudioMaterial(
            resource_path,
            media_info.get('duration_microseconds')
        )

        return AudioSegment(
            material=audio_material,
            target_timerange=convert_dict_to_object(segment_data.get('real_target_timerange'), Timerange),
            source_timerange=convert_dict_to_object(segment_data.get('source_timerange'), Timerange),
            speed=segment_data.get('speed', DEFAULT_SPEED),
            volume=segment_data.get('volume', DEFAULT_VOLUME)
        )


class TextTrackProcessor(TrackProcessor):
    """
    文本轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('track_name')
        logger.info(f"开始处理文本轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.text)

        text_segments = track_data.get('text_segments')
        if text_segments:
            self._process_text_segments(script, track_name, text_segments)

    def _process_text_segments(self, script: draft.ScriptFile, track_name: str,
                               text_segments: List[Dict[str, Any]]) -> None:
        """处理文本片段列表"""
        for segment_data in text_segments:
            text = segment_data.get('content')
            if text:
                try:
                    text_segment = self._create_text_segment(segment_data)
                    self._apply_text_animations_and_effects(text_segment, segment_data)
                    script.add_segment(text_segment, track_name=track_name)
                    logger.info(f"成功添加文本片段到轨道: {track_name}")
                except Exception as e:
                    logger.error(f"处理文本片段失败: {e}")
                    raise

    def _create_text_segment(self, segment_data: Dict[str, Any]) -> TextSegment:
        """创建文本片段"""
        font_data = segment_data.get('font', DEFAULT_FONT)
        font_effect = self._get_effect_from_data(font_data)

        return TextSegment(
            text=segment_data.get('content'),
            timerange=convert_dict_to_object(segment_data.get('real_target_timerange'), Timerange),
            font=font_effect,
            style=convert_dict_to_object(segment_data.get('style'), TextStyle),
            border=convert_dict_to_object(segment_data.get('border'), TextBorder),
            clip_settings=convert_dict_to_object(segment_data.get('clip_settings'), ClipSettings),
            background=convert_dict_to_object(segment_data.get('background'), TextBackground)
        )

    def _apply_text_animations_and_effects(self, text_segment: TextSegment, segment_data: Dict[str, Any]) -> None:
        """应用文本动画和效果"""
        text_animations = segment_data.get('text_animation_and_effects')
        if text_animations:
            for animation_data in text_animations:
                self._apply_single_text_effect(text_segment, animation_data)

    def _apply_single_text_effect(self, text_segment: TextSegment, animation_data: Dict[str, Any]) -> None:
        """应用单个文本效果"""
        # 处理动画效果
        animation_type = animation_data.get("type")
        if animation_type:
            duration = animation_data.get("duration", DEFAULT_ANIMATION_DURATION)
            effect = self._get_effect_from_data(animation_type)
            if effect:
                text_segment.add_animation(effect, duration)
                logger.info("应用文本动画效果")

        # 处理气泡效果
        bubble_effect_id = animation_data.get("bubble_effect_id")
        bubble_resource_id = animation_data.get("bubble_resource_id")
        if bubble_effect_id and bubble_resource_id:
            text_segment.add_bubble(bubble_effect_id, bubble_resource_id)
            logger.info("应用文本气泡效果")

        # 处理花字效果
        flower_effect_id = animation_data.get("flower_effect_id")
        if flower_effect_id:
            text_segment.add_effect(flower_effect_id)
            logger.info("应用文本花字效果")


class VideoTrackProcessor(TrackProcessor):
    """
    视频轨道处理器
    """

    def process_track(self, script: draft.ScriptFile, track_data: Dict[str, Any]) -> None:
        track_name = track_data.get('track_name')
        logger.info(f"开始处理视频轨道: {track_name}")
        self.add_track_to_script(script, track_data, draft.TrackType.video)

        video_segments = track_data.get('video_segments')
        if video_segments:
            self._process_video_segments(script, track_name, video_segments)

    def _process_video_segments(self, script: draft.ScriptFile, track_name: str,
                                video_segments: List[Dict[str, Any]]) -> None:
        """处理视频片段列表"""
        for segment_data in video_segments:
            try:
                video_segment = self._create_video_segment(segment_data)
                self._apply_video_effects(video_segment, segment_data)
                script.add_segment(video_segment, track_name=track_name)
                logger.info(f"成功添加视频片段到轨道: {track_name}")
            except Exception as e:
                logger.error(f"处理视频片段失败: {e}")
                raise

    def _create_video_segment(self, segment_data: Dict[str, Any]) -> VideoSegment:
        """创建视频片段"""
        resource_path = segment_data.get('resource_path')
        media_info = segment_data.get('media_info', {})

        video_material = VideoMaterial(
            path=resource_path,
            material_name=media_info.get('file_name'),
            crop_settings=CropSettings()
        )

        return VideoSegment(
            material=video_material,
            target_timerange=convert_dict_to_object(segment_data.get('real_target_timerange'), Timerange),
            source_timerange=convert_dict_to_object(segment_data.get('source_timerange'), Timerange),
            speed=segment_data.get('speed', DEFAULT_SPEED),
            volume=segment_data.get('volume', DEFAULT_VOLUME),
            clip_settings=convert_dict_to_object(segment_data.get('clip_settings'), ClipSettings)
        )

    def _apply_video_effects(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用视频效果"""
        self._apply_transition_effect(video_segment, segment_data)
        self._apply_animation_effect(video_segment, segment_data)
        self._apply_background_filling(video_segment, segment_data)
        self._add_video_effect(video_segment, segment_data)
        self._add_filter(video_segment, segment_data)
        self._add_mask(video_segment, segment_data)

    def _add_mask(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加蒙版效果"""
        video_mask_data = segment_data.get('video_mask')
        if video_mask_data:
            mask_type = video_mask_data.get('mask_type')
            mask_effect = self._get_effect_from_data(mask_type)
            if mask_effect:
                video_segment.add_mask(mask_effect,
                                       center_x=video_mask_data.get("center_x", 0.0),
                                       center_y=video_mask_data.get("center_y", 0.0),
                                       size=video_mask_data.get("size", 0.5),
                                       rotation=video_mask_data.get("rotation", 0.0),
                                       feather=video_mask_data.get("feather", 0.0),
                                       invert=video_mask_data.get("invert", False),
                                       rect_width=video_mask_data.get("rect_width", None),
                                       round_corner=video_mask_data.get("round_corner", None)
                                       )
                logger.info("应用视频蒙版效果")

    def _add_filter(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加滤镜效果"""
        video_filters_data = segment_data.get('video_filters', [])
        for filter_data in video_filters_data:
            filter_type = filter_data.get('filter_type', {})
            filter_effect = self._get_effect_from_data(filter_type)
            if filter_effect:
                video_segment.add_filter(filter_effect, filter_type.get("intensity"))
                logger.info("应用滤镜效果")

    def _add_video_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """添加视频特效"""
        video_effects_data = segment_data.get('video_effects')
        if video_effects_data:
            for effect_data in video_effects_data:
                effect_type = effect_data.get('effect_type', {})
                video_effect = self._get_effect_from_data(effect_type)
                if video_effect:
                    video_segment.add_effect(video_effect, effect_type.get("params"))
                    logger.info("应用视频特效")

    def _apply_transition_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用转场效果"""
        transition_data = segment_data.get('transition_type')
        if transition_data:
            effect_info = transition_data.get('transition_type')
            transition_effect = self._get_effect_from_data(effect_info)
            duration = transition_data.get('real_duration')
            if transition_effect:
                video_segment.add_transition(transition_type=transition_effect, duration=duration)
                logger.info("应用视频转场效果")

    def _apply_animation_effect(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用动画效果"""
        animation_data = segment_data.get("animation")
        if animation_data:
            animation_type = animation_data.get('type')
            animation_effect = self._get_effect_from_data(animation_type)
            duration = animation_data.get('real_duration')
            if animation_effect:
                video_segment.add_animation(animation_type=animation_effect, duration=duration)
                logger.info("应用视频动画效果")

    def _apply_background_filling(self, video_segment: VideoSegment, segment_data: Dict[str, Any]) -> None:
        """应用背景填充效果"""
        background_data = segment_data.get("background_filling")
        if background_data:
            fill_type = background_data.get("fill_type")
            blur = background_data.get("blur", 0.0625)
            color = background_data.get("color", "#00000000")
            if fill_type:
                video_segment.add_background_filling(fill_type=fill_type, blur=blur, color=color)
                logger.info("应用视频背景填充")


class DraftService:
    """
    草稿服务类
    """

    def __init__(self, config: DraftConfig = None):
        self.data_store = DataStore()
        self.config = config or DraftConfig()
        self._initialize_processors()

    def _initialize_processors(self) -> None:
        """
        初始化各种轨道处理器
        """
        self.processors = {
            'audio': AudioTrackProcessor(self.config),
            'text': TextTrackProcessor(self.config),
            'video': VideoTrackProcessor(self.config)
        }
        logger.info("轨道处理器初始化完成")

    def create_draft_script(self, req_dto: DraftCreateReqDto) -> DraftCreateRepDto:
        """创建草稿"""
        draft_id = str(uuid.uuid4()).upper()
        
        draft_entity = DraftEntity(
            id=draft_id,
            draft_name=req_dto.name,
            width=req_dto.width,
            height=req_dto.height,
            fps=req_dto.fps,
            draft_path=req_dto.draftPath  # 这里使用draftPath从DTO映射到draft_path
        )
        
        # 创建草稿目录
        os.makedirs(req_dto.draftPath, exist_ok=True)
        
        # 保存草稿信息
        self.data_store.save_draft(draft_entity)
        
        # 保存草稿JSON文件
        draft_file_path = os.path.join(req_dto.draftPath, "draft.json")
        with open(draft_file_path, 'w', encoding='utf-8') as f:
            # 使用 model_dump 并处理 datetime 序列化
            draft_data = draft_entity.model_dump()
            # 手动处理 datetime 字段
            if 'create_time' in draft_data:
                draft_data['create_time'] = draft_data['create_time'].isoformat()
            if 'update_time' in draft_data:
                draft_data['update_time'] = draft_data['update_time'].isoformat()
            json.dump(draft_data, f, ensure_ascii=False, indent=2)
        
        return DraftCreateRepDto(
            draftId=draft_id,
            draftPath=req_dto.draftPath
        )

    def create_script(self, draft_id: str, width: int = None, height: int = None, fps: int = None) -> draft.ScriptFile:
        """
        创建新的剪映脚本文件

        Args:
            draft_id: 草稿ID
            width: 视频宽度，如果为None则使用配置中的默认值
            height: 视频高度，如果为None则使用配置中的默认值
            fps: 帧率，如果为None则使用配置中的默认值

        Returns:
            创建的脚本文件对象
        """
        width = width or self.config.default_width
        height = height or self.config.default_height
        fps = fps or self.config.default_fps
        script = draft.ScriptFile(width=width, height=height, fps=fps)
        script.content['id'] = draft_id
        logger.info(f"创建新脚本文件，ID: {draft_id}, 分辨率: {width}x{height}, 帧率: {fps}")
        return script

    def process_tracks(self, script: draft.ScriptFile, audio_tracks: List[Dict[str, Any]] = None,
                       text_tracks: List[Dict[str, Any]] = None, video_tracks: List[Dict[str, Any]] = None) -> None:
        """
        处理所有轨道数据

        Args:
            script: 脚本文件对象
            audio_tracks: 音频轨道数据列表
            text_tracks: 文本轨道数据列表
            video_tracks: 视频轨道数据列表
        """
        logger.info("开始处理所有轨道数据")
        if audio_tracks:
            self._process_track_list(script, audio_tracks, 'audio')
        if text_tracks:
            self._process_track_list(script, text_tracks, 'text')
        if video_tracks:
            self._process_track_list(script, video_tracks, 'video')
        logger.info("所有轨道数据处理完成")

    def _process_track_list(self, script: draft.ScriptFile, tracks: List[Dict[str, Any]], track_type: str) -> None:
        """
        处理指定类型的轨道列表

        Args:
            script: 脚本文件对象
            tracks: 轨道数据列表
            track_type: 轨道类型
        """
        processor = self.processors.get(track_type)
        if not processor:
            logger.error(f"未找到轨道类型 {track_type} 的处理器")
            return
        for track_data in tracks:
            try:
                processor.process_track(script, track_data)
            except Exception as e:
                logger.error(f"处理 {track_type} 轨道失败: {e}")
                raise

    def export_script_json(self, script: draft.ScriptFile) -> Dict[str, Any]:
        """
        导出脚本为JSON格式

        Args:
            script: 脚本文件对象

        Returns:
            JSON格式的脚本字典

        Raises:
            Exception: 当导出失败时抛出异常
        """
        try:
            result = json.loads(script.dumps())
            logger.info("脚本导出为JSON格式成功")
            return result
        except Exception as e:
            logger.error(f"脚本导出失败: {e}")
            raise

    def generate_meta_info(self, draft_id: str, draft_entity: DraftEntity = None) -> Dict[str, Any]:
        """
        生成草稿元数据信息

        Args:
            draft_id: 草稿ID
            draft_entity: 草稿实体对象，如果为None则从数据存储中获取

        Returns:
            元数据字典
        """
        try:
            if draft_entity is None:
                draft_entity = self.data_store.get_draft(draft_id)
                if not draft_entity:
                    raise SysException(404, f"未找到草稿: {draft_id}")

            meta_info = {
                "draft_id": draft_id,
                "draft_name": draft_entity.draft_name,
                "width": draft_entity.width,
                "height": draft_entity.height,
                "fps": draft_entity.fps,
                "draft_path": draft_entity.draft_path,
                "create_time": draft_entity.create_time.isoformat() if draft_entity.create_time else None,
                "update_time": draft_entity.update_time.isoformat() if draft_entity.update_time else None,
                "export_time": datetime.now().isoformat(),
                "version": "1.0"
            }

            logger.info(f"生成草稿元数据成功，草稿ID: {draft_id}")
            return meta_info
        except Exception as e:
            logger.error(f"生成草稿元数据失败: {e}")
            raise
