"""
音频特效服务
处理音频特效相关的业务逻辑
"""
from typing import List, Optional
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.domain.req.audio_effect_req_dto import AudioEffectReqDto
from jianyingdraft_python.entity.audio_segment_entity import AudioSegmentEntity
from jianyingdraft_python.exception.sys_exception import SysException


class AudioEffectService:
    """音频特效服务类"""
    
    def __init__(self):
        self.data_store: Optional[DataStore] = None
    
    def add_audio_effects(self, req_dto: AudioEffectReqDto) -> AudioSegmentEntity:
        """为音频片段添加特效"""
        # 获取音频片段
        segment = self._get_audio_segment(req_dto.draft_id, req_dto.segment_id)
        
        # 添加特效（合并新特效到现有特效列表）
        if segment.audio_effects is None:
            segment.audio_effects = []
        
        # 根据resourceId去重，同一类型特效只能有一个
        existing_effect_types = {effect.effect_type.resource_id for effect in segment.audio_effects}
        
        for new_effect in req_dto.audio_effects:
            if new_effect.effect_type.resource_id not in existing_effect_types:
                segment.audio_effects.append(new_effect)
            else:
                # 替换现有的同类型特效
                segment.audio_effects = [
                    effect for effect in segment.audio_effects 
                    if effect.effect_type.resource_id != new_effect.effect_type.resource_id
                ]
                segment.audio_effects.append(new_effect)
        
        # 保存更新后的片段
        self.data_store.save_audio_segment(segment)
        
        return segment
    
    def remove_audio_effects(self, draft_id: str, segment_id: str) -> AudioSegmentEntity:
        """移除音频片段的所有特效"""
        # 获取音频片段
        segment = self._get_audio_segment(draft_id, segment_id)
        
        # 清空特效列表
        segment.audio_effects = []
        
        # 保存更新后的片段
        self.data_store.save_audio_segment(segment)
        
        return segment
    
    def get_audio_effects(self, draft_id: str, segment_id: str) -> List:
        """获取音频片段的所有特效"""
        # 获取音频片段
        segment = self._get_audio_segment(draft_id, segment_id)
        
        # 返回特效信息
        effects_info = {
            "audio_effects": segment.audio_effects or []
        }
        
        return [effects_info]
    
    def _get_audio_segment(self, draft_id: str, segment_id: str) -> AudioSegmentEntity:
        """获取音频片段"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")
        
        # 获取音频片段
        segment = None
        for seg_id, seg in self.data_store._audio_segments.items():
            if seg.id == segment_id and seg.draft_id == draft_id:
                segment = seg
                break
        
        if not segment:
            raise SysException.not_found("音频片段不存在")
        
        return segment
