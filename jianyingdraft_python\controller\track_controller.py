from fastapi import APIRouter, HTTPException
from typing import List
from jianyingdraft_python.service.track_service import TrackService
from jianyingdraft_python.storage.storage_manager import get_data_store
from jianyingdraft_python.domain.rep.data_response import DataResponse
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.entity.track_entity import TrackEntity
from jianyingdraft_python.exception.sys_exception import SysException

router = APIRouter(prefix="/track", tags=["轨道管理"])

# 使用集中式存储管理器
print("🔧 TrackController: 初始化服务...")
track_service = TrackService()
track_service.data_store = get_data_store()
print("✅ TrackController: 服务初始化完成")


@router.post("/add", response_model=DataResponse[TrackEntity])
async def add_track(req_dto: TrackAddReqDto):
    """添加轨道"""
    try:
        result = track_service.add_track(req_dto)
        return DataResponse.success(result)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list/{draft_id}", response_model=DataResponse[List[TrackEntity]])
async def get_tracks(draft_id: str):
    """获取草稿的轨道"""
    try:
        tracks = track_service.get_tracks_by_draft(draft_id)
        return DataResponse.success(tracks)
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{track_id}", response_model=DataResponse[str])
async def delete_track(track_id: str):
    """删除轨道"""
    try:
        track_service.delete_track(track_id)
        return DataResponse.success("轨道删除成功")
    except SysException as e:
        raise HTTPException(status_code=e.code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))