"""
资源验证服务
提供统一的资源存在性验证功能
"""
import os
from pathlib import Path
from typing import List, Dict, Set, Optional
from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.config.app_config import get_app_config


class ResourceValidationService:
    """资源验证服务类"""
    
    def __init__(self):
        self.config = get_app_config()
        self._resource_cache: Dict[str, Set[str]] = {}
        self._load_available_resources()
    
    def _load_available_resources(self):
        """加载可用资源列表"""
        # 定义资源文件夹结构
        resource_folders = {
            "effects": "effects",           # 特效资源
            "filters": "filters",           # 滤镜资源
            "masks": "masks",               # 蒙版资源
            "transitions": "transitions",   # 转场资源
            "animations": "animations",     # 动画资源
            "fonts": "fonts",               # 字体资源
            "audio_effects": "audio_effects" # 音频特效资源
        }
        
        # 资源根目录（可以配置）
        resources_root = self.config.project_root / "resources"
        
        for category, folder_name in resource_folders.items():
            resource_folder = resources_root / folder_name
            resource_ids = set()
            
            if resource_folder.exists():
                # 扫描文件夹中的资源文件
                for file_path in resource_folder.rglob("*"):
                    if file_path.is_file():
                        # 使用文件名（不含扩展名）作为resource_id
                        resource_id = file_path.stem
                        resource_ids.add(resource_id)
            else:
                # 如果资源文件夹不存在，创建默认资源列表
                resource_ids = self._get_default_resources(category)
            
            self._resource_cache[category] = resource_ids
    
    def _get_default_resources(self, category: str) -> Set[str]:
        """获取默认资源列表（当资源文件夹不存在时）"""
        default_resources = {
            "effects": {
                "blur_effect", "glow_effect", "sharpen_effect", "noise_effect",
                "vintage_effect", "sepia_effect", "black_white_effect"
            },
            "filters": {
                "vintage_filter", "beauty_filter", "warm_filter", "cool_filter",
                "bright_filter", "contrast_filter", "saturation_filter"
            },
            "masks": {
                "circle_mask", "rectangle_mask", "heart_mask", "star_mask",
                "custom_mask"
            },
            "transitions": {
                "fade_transition", "slide_transition", "zoom_transition",
                "rotate_transition", "dissolve_transition"
            },
            "animations": {
                "fade_in", "fade_out", "slide_in", "slide_out", "zoom_in",
                "zoom_out", "rotate_in", "rotate_out"
            },
            "fonts": {
                "default_font", "arial", "times_new_roman", "helvetica",
                "comic_sans", "impact"
            },
            "audio_effects": {
                "echo_effect", "reverb_effect", "chorus_effect", "distortion_effect",
                "noise_reduction", "bass_boost", "treble_boost"
            }
        }
        
        return default_resources.get(category, set())
    
    def validate_resource(self, resource: Resource, category: str) -> Dict[str, any]:
        """
        验证单个资源
        
        Args:
            resource: 要验证的资源对象
            category: 资源类别 (effects, filters, masks, transitions, animations, fonts, audio_effects)
        
        Returns:
            验证结果字典，包含 is_valid, warning_message 等信息
        """
        if not resource or not resource.resource_id:
            return {
                "is_valid": False,
                "warning_message": f"资源ID不能为空",
                "resource_id": None,
                "category": category
            }
        
        available_resources = self._resource_cache.get(category, set())
        is_valid = resource.resource_id in available_resources
        
        result = {
            "is_valid": is_valid,
            "resource_id": resource.resource_id,
            "resource_name": resource.resource_name,
            "category": category
        }
        
        if not is_valid:
            result["warning_message"] = (
                f"资源 '{resource.resource_id}' 在类别 '{category}' 中不存在。"
                f"可用资源: {', '.join(sorted(available_resources)[:5])}..."
                if available_resources else f"类别 '{category}' 中没有可用资源"
            )
        
        return result
    
    def validate_resources(self, resources: List[Resource], category: str) -> Dict[str, any]:
        """
        批量验证资源
        
        Args:
            resources: 要验证的资源列表
            category: 资源类别
        
        Returns:
            批量验证结果
        """
        if not resources:
            return {
                "all_valid": True,
                "valid_count": 0,
                "invalid_count": 0,
                "warnings": []
            }
        
        results = []
        warnings = []
        
        for resource in resources:
            result = self.validate_resource(resource, category)
            results.append(result)
            
            if not result["is_valid"]:
                warnings.append(result["warning_message"])
        
        valid_count = sum(1 for r in results if r["is_valid"])
        invalid_count = len(results) - valid_count
        
        return {
            "all_valid": invalid_count == 0,
            "valid_count": valid_count,
            "invalid_count": invalid_count,
            "warnings": warnings,
            "results": results
        }
    
    def get_available_resources(self, category: str) -> List[str]:
        """获取指定类别的可用资源列表"""
        return sorted(list(self._resource_cache.get(category, set())))
    
    def get_all_categories(self) -> List[str]:
        """获取所有资源类别"""
        return list(self._resource_cache.keys())
    
    def refresh_resources(self):
        """刷新资源缓存"""
        self._resource_cache.clear()
        self._load_available_resources()


# 全局资源验证服务实例
_resource_validation_service = None


def get_resource_validation_service() -> ResourceValidationService:
    """获取资源验证服务实例（单例模式）"""
    global _resource_validation_service
    if _resource_validation_service is None:
        _resource_validation_service = ResourceValidationService()
    return _resource_validation_service
