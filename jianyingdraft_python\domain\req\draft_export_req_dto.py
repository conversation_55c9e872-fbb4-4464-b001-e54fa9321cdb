from pydantic import BaseModel
from typing import List, Dict, Any, Optional


class DraftExportReqDto(BaseModel):
    """
    草稿导出请求DTO
    """
    draftId: str
    width: Optional[int] = 1920
    height: Optional[int] = 1080
    fps: Optional[int] = 30
    audioTracks: Optional[List[Dict[str, Any]]] = []
    textTracks: Optional[List[Dict[str, Any]]] = []
    videoTracks: Optional[List[Dict[str, Any]]] = []

    class Config:
        json_encoders = {
            # 可以在这里添加自定义编码器
        }
        schema_extra = {
            "example": {
                "draftId": "DRAFT-123456",
                "width": 1920,
                "height": 1080,
                "fps": 30,
                "audioTracks": [
                    {
                        "trackName": "音频轨道1",
                        "relativeIndex": 0,
                        "absoluteIndex": 0,
                        "mute": False,
                        "audioSegments": []
                    }
                ],
                "textTracks": [
                    {
                        "trackName": "文本轨道1",
                        "relativeIndex": 0,
                        "absoluteIndex": 1,
                        "mute": False,
                        "textSegments": []
                    }
                ],
                "videoTracks": [
                    {
                        "trackName": "视频轨道1",
                        "relativeIndex": 0,
                        "absoluteIndex": 2,
                        "mute": False,
                        "videoSegments": []
                    }
                ]
            }
        }
