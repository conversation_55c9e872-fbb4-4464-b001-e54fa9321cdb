"""
转场效果服务
处理转场效果相关的业务逻辑
"""
import uuid
from typing import List, Optional
from jianyingdraft_python.storage.data_store import DataStore
from jianyingdraft_python.domain.req.transition_req_dto import TransitionReqDto
from jianyingdraft_python.entity.transition_entity import TransitionEntity
from jianyingdraft_python.exception.sys_exception import SysException


class TransitionService:
    """转场效果服务类"""
    
    def __init__(self):
        self.data_store: Optional[DataStore] = None
    
    def add_transition(self, req_dto: TransitionReqDto) -> TransitionEntity:
        """添加转场效果"""
        # 检查草稿是否存在
        draft = self.data_store.get_draft(req_dto.draft_id)
        if not draft:
            raise SysException.not_found("草稿不存在")
        
        # 检查起始和目标片段是否存在
        from_segment = self._get_segment_by_id(req_dto.from_segment_id, req_dto.draft_id)
        to_segment = self._get_segment_by_id(req_dto.to_segment_id, req_dto.draft_id)
        
        if not from_segment:
            raise SysException.not_found("起始片段不存在")
        if not to_segment:
            raise SysException.not_found("目标片段不存在")
        
        # 创建转场效果
        transition_id = req_dto.id or str(uuid.uuid4()).upper()
        
        transition = TransitionEntity(
            id=transition_id,
            draft_id=req_dto.draft_id,
            from_segment_id=req_dto.from_segment_id,
            to_segment_id=req_dto.to_segment_id,
            transition_type=req_dto.transition_type,
            duration=req_dto.duration,
            track_id=req_dto.track_id
        )
        
        # 保存转场效果
        self.data_store.save_transition(transition)
        
        return transition
    
    def get_transitions_by_draft(self, draft_id: str) -> List[TransitionEntity]:
        """获取草稿的所有转场效果"""
        return self.data_store.get_transitions_by_draft(draft_id)
    
    def delete_transition(self, transition_id: str):
        """删除转场效果"""
        # 检查转场是否存在
        transition = None
        for t in self.data_store._transitions.values():
            if t.id == transition_id:
                transition = t
                break
        
        if not transition:
            raise SysException.not_found("转场效果不存在")
        
        # 删除转场效果
        self.data_store.delete_transition(transition_id)
    
    def update_transition(self, req_dto: TransitionReqDto) -> TransitionEntity:
        """更新转场效果"""
        if not req_dto.id:
            raise SysException.bad_request("更新转场效果需要提供ID")
        
        # 检查转场是否存在
        existing_transition = None
        for t in self.data_store._transitions.values():
            if t.id == req_dto.id:
                existing_transition = t
                break
        
        if not existing_transition:
            raise SysException.not_found("转场效果不存在")
        
        # 更新转场效果
        updated_transition = TransitionEntity(
            id=req_dto.id,
            draft_id=req_dto.draft_id,
            from_segment_id=req_dto.from_segment_id,
            to_segment_id=req_dto.to_segment_id,
            transition_type=req_dto.transition_type,
            duration=req_dto.duration,
            track_id=req_dto.track_id,
            create_time=existing_transition.create_time  # 保持原创建时间
        )
        
        # 保存更新后的转场效果
        self.data_store.save_transition(updated_transition)
        
        return updated_transition
    
    def _get_segment_by_id(self, segment_id: str, draft_id: str):
        """根据ID获取片段（视频或音频）"""
        # 在视频片段中查找
        for seg in self.data_store._video_segments.values():
            if seg.id == segment_id and seg.draft_id == draft_id:
                return seg
        
        # 在音频片段中查找
        for seg in self.data_store._audio_segments.values():
            if seg.id == segment_id and seg.draft_id == draft_id:
                return seg
        
        # 在文本片段中查找
        for seg in self.data_store._text_segments.values():
            if seg.id == segment_id and seg.draft_id == draft_id:
                return seg
        
        return None
