"""
测试参数修复和资源验证功能
验证所有DTO参数与Kotlin版本一致，以及资源验证机制
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from jianyingdraft_python.controller.draft_controller import draft_service
from jianyingdraft_python.controller.track_controller import track_service
from jianyingdraft_python.controller.video_segment_controller import video_service
from jianyingdraft_python.controller.resource_validation_controller import resource_validation_service

from jianyingdraft_python.domain.req.draft_create_req_dto import DraftCreateReqDto
from jianyingdraft_python.domain.req.track_add_req_dto import TrackAddReqDto
from jianyingdraft_python.domain.req.media_segment_add_req_dto import MediaSegmentAddReqDto
from jianyingdraft_python.domain.req.background_filling_req_dto import BackgroundFillingReqDto
from jianyingdraft_python.domain.req.video_effect_req_dto import VideoEffectReqDto
from jianyingdraft_python.domain.req.video_filter_req_dto import VideoFilterReqDto
from jianyingdraft_python.domain.req.video_mask_req_dto import VideoMaskReqDto

from jianyingdraft_python.domain.req.resource import Resource
from jianyingdraft_python.domain.video.video_effect import VideoEffect
from jianyingdraft_python.domain.video.video_filter import VideoFilter
from jianyingdraft_python.domain.video.video_mask import VideoMask
from jianyingdraft_python.domain.timerange import Timerange


def test_parameter_consistency():
    """测试参数与Kotlin版本的一致性"""
    print("📋 测试DTO参数与Kotlin版本的一致性")
    print("=" * 80)
    
    # 测试BackgroundFillingReqDto参数
    print("1. 测试BackgroundFillingReqDto参数...")
    
    # 使用Kotlin版本的字段名
    bg_filling_data = {
        "draftId": "test-draft-123",
        "videoSegmentId": "test-segment-456",
        "fillType": "blur",
        "blur": 0.625,
        "color": "#00000000"
    }
    
    try:
        bg_filling_req = BackgroundFillingReqDto(**bg_filling_data)
        print(f"   ✅ BackgroundFillingReqDto创建成功")
        print(f"   📊 参数: draftId={bg_filling_req.draft_id}, fillType={bg_filling_req.fill_type}")
        print(f"   📊 参数: blur={bg_filling_req.blur}, color={bg_filling_req.color}")
    except Exception as e:
        print(f"   ❌ BackgroundFillingReqDto创建失败: {e}")
        return False
    
    # 测试VideoEffectReqDto参数
    print("\n2. 测试VideoEffectReqDto参数...")
    
    effect_data = {
        "draftId": "test-draft-123",
        "segmentId": "test-segment-456",
        "effects": [
            {
                "effect_type": {
                    "resource_id": "blur_effect",
                    "resource_name": "模糊效果"
                },
                "params": [50.0, 10.0]
            }
        ]
    }
    
    try:
        effect_req = VideoEffectReqDto(**effect_data)
        print(f"   ✅ VideoEffectReqDto创建成功")
        print(f"   📊 参数: draftId={effect_req.draft_id}, segmentId={effect_req.segment_id}")
    except Exception as e:
        print(f"   ❌ VideoEffectReqDto创建失败: {e}")
        return False
    
    print("\n✅ 所有DTO参数与Kotlin版本一致！")
    return True


def test_resource_validation():
    """测试资源验证功能"""
    print("\n🔍 测试资源验证功能")
    print("=" * 80)
    
    # 1. 测试资源类别获取
    print("1. 测试资源类别获取...")
    try:
        categories = resource_validation_service.get_all_categories()
        print(f"   ✅ 可用资源类别: {categories}")
    except Exception as e:
        print(f"   ❌ 获取资源类别失败: {e}")
        return False
    
    # 2. 测试可用资源获取
    print("\n2. 测试可用资源获取...")
    try:
        effects = resource_validation_service.get_available_resources("effects")
        filters = resource_validation_service.get_available_resources("filters")
        print(f"   ✅ 可用特效: {effects[:5]}...")
        print(f"   ✅ 可用滤镜: {filters[:5]}...")
    except Exception as e:
        print(f"   ❌ 获取可用资源失败: {e}")
        return False
    
    # 3. 测试有效资源验证
    print("\n3. 测试有效资源验证...")
    try:
        valid_resource = Resource(resource_id="blur_effect", resource_name="模糊效果")
        result = resource_validation_service.validate_resource(valid_resource, "effects")
        print(f"   ✅ 有效资源验证: {result['is_valid']}")
    except Exception as e:
        print(f"   ❌ 有效资源验证失败: {e}")
        return False
    
    # 4. 测试无效资源验证
    print("\n4. 测试无效资源验证...")
    try:
        invalid_resource = Resource(resource_id="nonexistent_effect", resource_name="不存在的特效")
        result = resource_validation_service.validate_resource(invalid_resource, "effects")
        print(f"   ✅ 无效资源验证: {result['is_valid']}")
        if not result['is_valid']:
            print(f"   ⚠️ 警告信息: {result['warning_message']}")
    except Exception as e:
        print(f"   ❌ 无效资源验证失败: {e}")
        return False
    
    print("\n✅ 资源验证功能正常工作！")
    return True


def test_integrated_workflow():
    """测试集成工作流程"""
    print("\n🧪 测试集成工作流程（参数修复 + 资源验证）")
    print("=" * 80)
    
    # 1. 创建测试草稿
    print("1. 创建测试草稿...")
    draft_req = DraftCreateReqDto(
        name="参数和资源验证测试草稿",
        width=1920,
        height=1080,
        fps=30,
        draftPath="/test/param_resource_test_draft"
    )
    
    try:
        draft = draft_service.create_draft_script(draft_req)
        draft_id = draft.draftId
        print(f"   ✅ 草稿创建成功: {draft_id}")
    except Exception as e:
        print(f"   ❌ 草稿创建失败: {e}")
        return False
    
    # 2. 创建测试轨道
    print("2. 创建测试轨道...")
    track_req = TrackAddReqDto(
        draftId=draft_id,
        trackType="video",
        trackName="参数和资源验证测试轨道",
        mute=False,
        relativeIndex=0,
        absoluteIndex=1
    )
    
    try:
        track = track_service.add_track(track_req)
        track_id = track.id
        print(f"   ✅ 轨道创建成功: {track_id}")
    except Exception as e:
        print(f"   ❌ 轨道创建失败: {e}")
        return False
    
    # 3. 创建视频片段
    print("3. 创建视频片段...")
    video_req = MediaSegmentAddReqDto(
        draftId=draft_id,
        targetTimerange=Timerange(start="0s", duration="5s"),
        resourcePath="/test/param_resource_test_video.mp4",
        trackId=track_id,
        speed=1.0,
        volume=1.0
    )
    
    try:
        video_segment = video_service.add_video_segment(video_req)
        video_segment_id = video_segment.id
        print(f"   ✅ 视频片段创建成功: {video_segment_id}")
    except Exception as e:
        print(f"   ❌ 视频片段创建失败: {e}")
        return False
    
    # 4. 测试背景填充（使用Kotlin兼容参数）
    print("4. 测试背景填充（Kotlin兼容参数）...")
    try:
        bg_filling_req = BackgroundFillingReqDto(
            draftId=draft_id,
            videoSegmentId=video_segment_id,
            fillType="blur",
            blur=0.8,
            color="#FF0000FF"
        )
        
        result = video_service.add_background_filling(bg_filling_req)
        print(f"   ✅ 背景填充添加成功: {result}")
    except Exception as e:
        print(f"   ❌ 背景填充添加失败: {e}")
    
    # 5. 测试特效（带资源验证）
    print("5. 测试特效（带资源验证）...")
    try:
        # 测试有效资源
        effect_req = VideoEffectReqDto(
            draftId=draft_id,
            segmentId=video_segment_id,
            effects=[VideoEffect(
                effect_type=Resource(resource_id="blur_effect", resource_name="模糊效果"),
                params=[50.0, 10.0]
            )]
        )
        
        result = video_service.add_effects(effect_req)
        print(f"   ✅ 有效特效添加成功: {result}")
        
        # 测试无效资源（应该有警告但不报错）
        invalid_effect_req = VideoEffectReqDto(
            draftId=draft_id,
            segmentId=video_segment_id,
            effects=[VideoEffect(
                effect_type=Resource(resource_id="invalid_effect", resource_name="无效特效"),
                params=[30.0, 5.0]
            )]
        )
        
        result = video_service.add_effects(invalid_effect_req)
        print(f"   ✅ 无效特效添加成功（带警告）: {result}")
        
    except Exception as e:
        print(f"   ❌ 特效添加失败: {e}")
    
    # 6. 测试滤镜（带资源验证）
    print("6. 测试滤镜（带资源验证）...")
    try:
        filter_req = VideoFilterReqDto(
            draftId=draft_id,
            segmentId=video_segment_id,
            filters=[VideoFilter(
                filter_type=Resource(resource_id="invalid_filter", resource_name="无效滤镜"),
                intensity=80.0
            )]
        )
        
        result = video_service.add_filters(filter_req)
        print(f"   ✅ 滤镜添加成功（带警告）: {result}")
    except Exception as e:
        print(f"   ❌ 滤镜添加失败: {e}")
    
    print("\n🎉 集成工作流程测试完成！")
    return True


if __name__ == "__main__":
    print("🎯 参数修复和资源验证功能测试")
    print("=" * 80)
    
    # 1. 测试参数一致性
    param_test = test_parameter_consistency()
    
    # 2. 测试资源验证
    resource_test = test_resource_validation()
    
    # 3. 测试集成工作流程
    workflow_test = test_integrated_workflow()
    
    print("\n" + "=" * 60)
    if param_test and resource_test and workflow_test:
        print("🎉 所有测试通过！")
        print("✅ DTO参数与Kotlin版本完全一致")
        print("✅ 资源验证机制正常工作")
        print("✅ 集成工作流程运行正常")
    else:
        print("❌ 部分测试失败！")
        if not param_test:
            print("❌ DTO参数一致性测试失败")
        if not resource_test:
            print("❌ 资源验证测试失败")
        if not workflow_test:
            print("❌ 集成工作流程测试失败")
